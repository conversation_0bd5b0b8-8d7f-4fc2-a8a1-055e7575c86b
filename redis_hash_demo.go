package main

import (
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"
)

// 全局 Redis 客户端变量
var redisClient *redis.Client

// 初始化 Redis 客户端连接
func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // Redis 服务器地址
		Password: "",               // Redis 密码（无密码则为空字符串）
		DB:       0,                // 使用的数据库编号，默认0
	})
}

func main() {
	// 创建上下文
	ctx := context.Background()

	fmt.Println("=== Redis Hash 类型操作示例 ===\n")

	// 1. 基本的 HSet 和 HGet 操作
	fmt.Println("1. 基本 HSet/HGet 操作:")
	hSetAndGetExample(ctx)

	// 2. HGetAll 操作 - 获取所有字段和值
	fmt.Println("\n2. HGetAll 操作:")
	hGetAllExample(ctx)

	// 3. HMSet 和 HMGet 操作 - 批量设置和获取
	fmt.Println("\n3. HMSet/HMGet 操作:")
	hmSetAndGetExample(ctx)

	// 4. 数字操作 - HIncrBy
	fmt.Println("\n4. 数字操作 - HIncrBy:")
	hIncrByExample(ctx)

	// 5. 其他操作 - HExists, HDel, HLen, HKeys
	fmt.Println("\n5. 其他操作:")
	otherHashOperationsExample(ctx)
}

// 1. 基本的 HSet 和 HGet 操作
func hSetAndGetExample(ctx context.Context) {
	// 清理可能存在的键，确保测试环境干净
	redisClient.Del(ctx, "user:1001")

	// HSet: 设置单个字段
	err := redisClient.HSet(ctx, "user:1001", "name", "张三").Err()
	if err != nil {
		fmt.Printf("HSet 失败: %v\n", err)
		return
	}
	fmt.Println("✓ HSet: 设置 user:1001 的 name 字段为 张三")

	// HGet: 获取单个字段
	name, err := redisClient.HGet(ctx, "user:1001", "name").Result()
	if err != nil {
		fmt.Printf("HGet 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ HGet: user:1001 的 name 字段值为 %s\n", name)

	// 设置多个字段
	err = redisClient.HSet(ctx, "user:1001", "age", 25, "city", "北京").Err()
	if err != nil {
		fmt.Printf("HSet 多字段失败: %v\n", err)
		return
	}
	fmt.Println("✓ HSet: 设置 user:1001 的 age=25, city=北京")
}

// 2. HGetAll 操作 - 获取所有字段和值
func hGetAllExample(ctx context.Context) {
	// 确保有数据
	redisClient.HSet(ctx, "user:1002", map[string]interface{}{
		"name":    "李四",
		"age":     30,
		"city":    "上海",
		"job":     "工程师",
		"hobby":   "读书",
		"married": "false",
	})

	// HGetAll: 获取所有字段和值
	allFields, err := redisClient.HGetAll(ctx, "user:1002").Result()
	if err != nil {
		fmt.Printf("HGetAll 失败: %v\n", err)
		return
	}

	fmt.Println("✓ HGetAll: 获取 user:1002 的所有字段和值:")
	for field, value := range allFields {
		fmt.Printf("  - %s: %s\n", field, value)
	}
}

// 3. HMSet 和 HMGet 操作 - 批量设置和获取
func hmSetAndGetExample(ctx context.Context) {
	// 清理可能存在的键
	redisClient.Del(ctx, "product:1001")

	// HMSet: 批量设置多个字段
	err := redisClient.HMSet(ctx, "product:1001", map[string]interface{}{
		"name":     "智能手机",
		"price":    2999,
		"brand":    "小米",
		"category": "电子产品",
		"stock":    100,
	}).Err()
	if err != nil {
		fmt.Printf("HMSet 失败: %v\n", err)
		return
	}
	fmt.Println("✓ HMSet: 批量设置 product:1001 的多个字段")

	// HMGet: 批量获取多个字段
	values, err := redisClient.HMGet(ctx, "product:1001", "name", "price", "brand").Result()
	if err != nil {
		fmt.Printf("HMGet 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ HMGet: product:1001 的字段值 - name=%v, price=%v, brand=%v\n", values[0], values[1], values[2])
}

// 4. 数字操作 - HIncrBy
func hIncrByExample(ctx context.Context) {
	// 清理可能存在的键
	redisClient.Del(ctx, "stats:website")

	// 设置初始值
	redisClient.HSet(ctx, "stats:website", "visits", 100, "likes", 50)
	fmt.Println("✓ 初始化 stats:website - visits=100, likes=50")

	// HIncrBy: 增加指定的值
	newVisits, err := redisClient.HIncrBy(ctx, "stats:website", "visits", 10).Result()
	if err != nil {
		fmt.Printf("HIncrBy 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ HIncrBy: 增加 visits 字段 10，新值为 %d\n", newVisits)

	// 再次增加
	newLikes, err := redisClient.HIncrBy(ctx, "stats:website", "likes", 5).Result()
	if err != nil {
		fmt.Printf("HIncrBy 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ HIncrBy: 增加 likes 字段 5，新值为 %d\n", newLikes)

	// 获取最终结果
	values, err := redisClient.HMGet(ctx, "stats:website", "visits", "likes").Result()
	if err != nil {
		fmt.Printf("HMGet 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 最终结果: visits=%v, likes=%v\n", values[0], values[1])
}

// 5. 其他操作 - HExists, HDel, HLen, HKeys
func otherHashOperationsExample(ctx context.Context) {
	// 清理可能存在的键
	redisClient.Del(ctx, "user:1003")

	// 设置一些字段
	redisClient.HMSet(ctx, "user:1003", map[string]interface{}{
		"name":     "王五",
		"age":      35,
		"city":     "广州",
		"email":    "<EMAIL>",
		"verified": "true",
	})
	fmt.Println("✓ 设置 user:1003 的多个字段")

	// HExists: 检查字段是否存在
	exists, err := redisClient.HExists(ctx, "user:1003", "email").Result()
	if err != nil {
		fmt.Printf("HExists 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ HExists: email 字段存在? %t\n", exists)

	exists, _ = redisClient.HExists(ctx, "user:1003", "phone").Result()
	fmt.Printf("✓ HExists: phone 字段存在? %t\n", exists)

	// HLen: 获取字段数量
	fieldCount, err := redisClient.HLen(ctx, "user:1003").Result()
	if err != nil {
		fmt.Printf("HLen 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ HLen: user:1003 有 %d 个字段\n", fieldCount)

	// HKeys: 获取所有字段名
	keys, err := redisClient.HKeys(ctx, "user:1003").Result()
	if err != nil {
		fmt.Printf("HKeys 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ HKeys: user:1003 的所有字段名: %v\n", keys)

	// HDel: 删除字段
	delCount, err := redisClient.HDel(ctx, "user:1003", "verified").Result()
	if err != nil {
		fmt.Printf("HDel 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ HDel: 删除 verified 字段，删除了 %d 个字段\n", delCount)

	// 再次获取所有字段
	allFields, _ := redisClient.HGetAll(ctx, "user:1003").Result()
	fmt.Println("✓ 删除后的所有字段和值:")
	for field, value := range allFields {
		fmt.Printf("  - %s: %s\n", field, value)
	}
}

package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// 全局 Redis 客户端变量
var redisClient *redis.Client

// 初始化 Redis 客户端连接
func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // Redis 服务器地址
		Password: "",               // Redis 密码（无密码则为空字符串）
		DB:       0,                // 使用的数据库编号，默认0
	})
}

func main() {
	// 创建上下文
	ctx := context.Background()

	fmt.Println("=== Redis String 类型练习 ===")

	// 练习1: 基础操作
	// 在 Redis 中存储一个键为 "username"，值为 "admin"，永不过期
	fmt.Println("\n练习1: 基础的 Set/Get 操作")
	err := redisClient.Set(ctx, "username", "admin", 0).Err()
	if err != nil {
		fmt.Println("设置失败:", err)
	} else {
		fmt.Println("✓ 设置成功: username = admin")
	}

	// 获取并打印值
	value, err := redisClient.Get(ctx, "username").Result()
	if err != nil {
		fmt.Println("获取失败:", err)
	} else {
		fmt.Println("✓ 获取成功: username =", value)
	}

	// 练习2: 过期时间
	// 存储一个会话令牌，键为 "session:123"，值为 "abcdef"，30分钟后过期
	fmt.Println("\n练习2: 设置过期时间")
	err = redisClient.Set(ctx, "session:123", "abcdef", 30*time.Minute).Err()
	if err != nil {
		fmt.Println("设置失败:", err)
	} else {
		fmt.Println("✓ 设置成功: session:123 = abcdef (30分钟后过期)")
	}

	// 查看剩余过期时间
	ttl, err := redisClient.TTL(ctx, "session:123").Result()
	if err != nil {
		fmt.Println("获取过期时间失败:", err)
	} else {
		fmt.Printf("✓ 剩余生存时间: %v\n", ttl)
	}

	// 练习3: 计数器
	// 实现一个页面访问计数器，键为 "page:visits"，初始值为0，然后增加3次
	fmt.Println("\n练习3: 计数器操作")

	// 先删除可能存在的键，确保从0开始
	redisClient.Del(ctx, "page:visits")

	// 设置初始值为0
	redisClient.Set(ctx, "page:visits", "0", 0)

	// 增加3次
	for i := 1; i <= 3; i++ {
		newCount, err := redisClient.Incr(ctx, "page:visits").Result()
		if err != nil {
			fmt.Println("增加计数失败:", err)
		} else {
			fmt.Printf("✓ 第 %d 次访问，计数: %d\n", i, newCount)
		}
	}

	// 获取最终计数
	finalCount, err := redisClient.Get(ctx, "page:visits").Int64()
	if err != nil {
		fmt.Println("获取计数失败:", err)
	} else {
		fmt.Println("✓ 最终计数:", finalCount)
	}

	// 练习4: 条件操作
	// 使用 SetNX 实现"只有当键不存在时才设置值"的逻辑
	fmt.Println("\n练习4: 条件操作 SetNX")

	// 先删除可能存在的键
	redisClient.Del(ctx, "unique_key")

	// 第一次设置（应该成功）
	success, err := redisClient.SetNX(ctx, "unique_key", "第一次设置", 0).Result()
	if err != nil {
		fmt.Println("SetNX 失败:", err)
	} else {
		fmt.Printf("✓ 第一次 SetNX: %t\n", success)
	}

	// 第二次设置（应该失败）
	success, err = redisClient.SetNX(ctx, "unique_key", "第二次设置", 0).Result()
	if err != nil {
		fmt.Println("SetNX 失败:", err)
	} else {
		fmt.Printf("✓ 第二次 SetNX: %t\n", success)
	}

	// 获取实际值
	value, err = redisClient.Get(ctx, "unique_key").Result()
	if err != nil {
		fmt.Println("获取失败:", err)
	} else {
		fmt.Printf("✓ 实际值: %s\n", value)
	}

	// 练习5: 批量操作
	// 一次性设置和获取用户的姓名、年龄和城市信息
	fmt.Println("\n练习5: 批量操作 MSet/MGet")

	// 批量设置
	err = redisClient.MSet(ctx, map[string]interface{}{
		"user:1:name": "张三",
		"user:1:age":  "25",
		"user:1:city": "北京",
	}).Err()
	if err != nil {
		fmt.Println("批量设置失败:", err)
	} else {
		fmt.Println("✓ 批量设置成功")
	}

	// 批量获取
	values, err := redisClient.MGet(ctx, "user:1:name", "user:1:age", "user:1:city").Result()
	if err != nil {
		fmt.Println("批量获取失败:", err)
	} else {
		fmt.Printf("✓ 批量获取: name=%v, age=%v, city=%v\n", values[0], values[1], values[2])
	}

	// 练习6: 获取并更新
	// 使用 GetSet 获取旧值并设置新值
	fmt.Println("\n练习6: GetSet 操作")

	// 确保有初始值
	redisClient.Set(ctx, "counter", "100", 0)

	// GetSet: 设置新值并返回旧值
	oldValue, err := redisClient.GetSet(ctx, "counter", "200").Result()
	if err != nil {
		fmt.Println("GetSet 失败:", err)
	} else {
		fmt.Printf("✓ 旧值: %s, 新值设置为: 200\n", oldValue)
	}

	// 验证新值
	newValue, err := redisClient.Get(ctx, "counter").Result()
	if err != nil {
		fmt.Println("获取新值失败:", err)
	} else {
		fmt.Printf("✓ 验证新值: %s\n", newValue)
	}
}

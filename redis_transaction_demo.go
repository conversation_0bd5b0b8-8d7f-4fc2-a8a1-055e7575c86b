package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

var redisClient *redis.Client

func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis 事务处理示例 ===\n")

	// 1. 基本事务示例
	fmt.Println("1. 基本事务示例:")
	basicTransaction(ctx)

	// 2. 使用 WATCH 的事务示例（乐观锁）
	fmt.Println("\n2. 使用 WATCH 的事务示例:")
	watchTransaction(ctx)

	// 3. 事务中的错误处理
	fmt.Println("\n3. 事务中的错误处理:")
	transactionErrorHandling(ctx)
}

// 1. 基本事务示例
func basicTransaction(ctx context.Context) {
	// 清理环境
	redisClient.Del(ctx, "account:A", "account:B")

	// 设置初始余额
	redisClient.Set(ctx, "account:A", "100", 0)
	redisClient.Set(ctx, "account:B", "50", 0)
	fmt.Println("✓ 初始化账户余额: A=100, B=50")

	// 开始事务 - 转账 30 元从 A 到 B
	fmt.Println("✓ 开始事务: 从账户A转账30元到账户B")

	// 使用 TxPipeline 进行事务操作
	pipe := redisClient.TxPipeline()

	// 在事务中执行多个命令
	pipe.DecrBy(ctx, "account:A", 30)
	pipe.IncrBy(ctx, "account:B", 30)

	// 执行事务
	_, err := pipe.Exec(ctx)
	if err != nil {
		fmt.Printf("❌ 事务执行失败: %v\n", err)
		return
	}

	// 查看结果
	balanceA, _ := redisClient.Get(ctx, "account:A").Int64()
	balanceB, _ := redisClient.Get(ctx, "account:B").Int64()
	fmt.Printf("✓ 事务执行成功! 当前余额: A=%d, B=%d\n", balanceA, balanceB)
}

// 2. 使用 WATCH 的事务示例（乐观锁）
func watchTransaction(ctx context.Context) {
	// 清理环境
	redisClient.Del(ctx, "stock:item:10086")

	// 设置初始库存
	redisClient.Set(ctx, "stock:item:10086", "100", 0)
	fmt.Println("✓ 初始化商品10086库存为100")

	// 模拟并发场景
	go func() {
		// 模拟其他客户端修改库存
		time.Sleep(100 * time.Millisecond)
		redisClient.DecrBy(ctx, "stock:item:10086", 10)
		fmt.Println("✓ 其他客户端减少了10个库存")
	}()

	// 主客户端尝试减库存
	fmt.Println("✓ 主客户端开始处理库存...")

	// 使用 Watch 监视库存键
	txf := func(tx *redis.Tx) error {
		// 获取当前库存
		stock, err := tx.Get(ctx, "stock:item:10086").Int64()
		if err != nil {
			return err
		}

		// 检查库存是否足够
		if stock < 20 {
			return fmt.Errorf("库存不足，当前库存: %d", stock)
		}

		// 库存足够，执行减库存操作
		_, err = tx.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
			pipe.DecrBy(ctx, "stock:item:10086", 20)
			return nil
		})
		return err
	}

	// 执行事务，如果 WATCH 的键被修改，会自动重试
	for i := 0; i < 3; i++ {
		err := redisClient.Watch(ctx, txf, "stock:item:10086")
		if err == nil {
			fmt.Println("✓ 库存减少成功!")
			break
		} else if i == 2 {
			fmt.Printf("❌ 库存操作失败: %v\n", err)
		} else {
			fmt.Printf("⚠️ 重试 %d: %v\n", i+1, err)
			time.Sleep(50 * time.Millisecond)
		}
	}

	// 查看最终库存
	finalStock, _ := redisClient.Get(ctx, "stock:item:10086").Int64()
	fmt.Printf("✓ 最终库存: %d\n", finalStock)
}

// 3. 事务中的错误处理
func transactionErrorHandling(ctx context.Context) {
	// 清理环境
	redisClient.Del(ctx, "counter", "user:profile")

	// 设置初始值
	redisClient.Set(ctx, "counter", "abc", 0) // 故意设置为非数字
	fmt.Println("✓ 设置 counter=abc (非数字值)")

	// 开始事务
	fmt.Println("✓ 开始事务，包含一个会失败的命令...")

	pipe := redisClient.TxPipeline()

	// 添加一些命令，其中一个会失败
	incrResult := pipe.IncrBy(ctx, "counter", 10) // 这会失败，因为 counter 不是数字
	pipe.HSet(ctx, "user:profile", "name", "张三")  // 这个命令正常

	// 执行事务
	_, err := pipe.Exec(ctx)

	// 检查整体事务错误
	if err != nil {
		fmt.Printf("⚠️ 事务执行有错误: %v\n", err)
	}

	// 检查特定命令的错误
	if err := incrResult.Err(); err != nil {
		fmt.Printf("❌ IncrBy 命令失败: %v\n", err)
	}

	// 查看结果
	counterVal, _ := redisClient.Get(ctx, "counter").Result()
	profileVal, _ := redisClient.HGetAll(ctx, "user:profile").Result()

	fmt.Printf("✓ 最终结果: counter=%s, user:profile=%v\n", counterVal, profileVal)
	fmt.Println("注意: Redis 事务不支持回滚，即使有命令失败，其他命令仍会执行")
}

package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// 全局 Redis 客户端变量
var redisClient *redis.Client

// 初始化 Redis 客户端连接
func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // Redis 服务器地址
		Password: "",               // Redis 密码（无密码则为空字符串）
		DB:       0,                // 使用的数据库编号，默认0
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis String 类型详解 ===\n")
	fmt.Println("String 是 Redis 最基本的数据类型，可以存储文本、数字、二进制数据等。")
	fmt.Println("最大可存储 512MB 的数据。")

	// 清理环境
	redisClient.Del(ctx, "user:token", "page:views", "product:stock", "distributed:lock", "cache:data", "session:123", "rate_limit:user:1001")

	// 1. 缓存场景 - 最常见的使用场景
	fmt.Println("\n1. 缓存场景示例:")
	fmt.Println("应用场景：缓存数据库查询结果、API响应、页面内容等")
	cacheExample(ctx)

	// 2. 计数器场景 - 利用原子性操作
	fmt.Println("\n2. 计数器场景示例:")
	fmt.Println("应用场景：页面访问量、点赞数、库存数量、用户积分等")
	counterExample(ctx)

	// 3. 分布式锁场景 - 保证并发安全
	fmt.Println("\n3. 分布式锁场景示例:")
	fmt.Println("应用场景：防止重复提交、秒杀活动、定时任务防重复执行等")
	distributedLockExample(ctx)

	// 4. 限流场景 - 控制访问频率
	fmt.Println("\n4. 限流场景示例:")
	fmt.Println("应用场景：API限流、防刷、用户操作频率控制等")
	rateLimitExample(ctx)

	// 5. 会话管理场景 - 用户状态管理
	fmt.Println("\n5. 会话管理场景示例:")
	fmt.Println("应用场景：用户登录状态、购物车、临时数据存储等")
	sessionManagementExample(ctx)

	// 6. 基本操作演示
	fmt.Println("\n6. 基本操作演示:")
	basicOperationsExample(ctx)
}

// 1. 缓存场景示例
func cacheExample(ctx context.Context) {
	cacheKey := "cache:user:profile:1001"
	
	// 模拟从数据库获取的用户数据（JSON格式）
	userProfile := `{"id":1001,"name":"张三","email":"<EMAIL>","age":28,"city":"北京"}`
	
	// 设置缓存，过期时间30分钟
	err := redisClient.Set(ctx, cacheKey, userProfile, 30*time.Minute).Err()
	if err != nil {
		fmt.Printf("❌ 设置缓存失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 缓存用户资料成功，过期时间: 30分钟\n")
	
	// 从缓存获取数据
	cachedData, err := redisClient.Get(ctx, cacheKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取缓存失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 从缓存获取用户资料: %s\n", cachedData)
	
	// 检查缓存剩余时间
	ttl, _ := redisClient.TTL(ctx, cacheKey).Result()
	fmt.Printf("✓ 缓存剩余时间: %v\n", ttl)
	
	fmt.Println("优势：大幅提升数据访问速度，减少数据库压力")
}

// 2. 计数器场景示例
func counterExample(ctx context.Context) {
	pageViewKey := "page:views:homepage"
	likeCountKey := "post:likes:12345"
	
	// 页面访问计数
	fmt.Println("📊 页面访问计数:")
	for i := 1; i <= 5; i++ {
		newCount, err := redisClient.Incr(ctx, pageViewKey).Result()
		if err != nil {
			fmt.Printf("❌ 计数失败: %v\n", err)
			continue
		}
		fmt.Printf("  第 %d 次访问，总访问量: %d\n", i, newCount)
	}
	
	// 点赞数增加
	fmt.Println("\n👍 点赞数操作:")
	// 增加3个赞
	newLikes, err := redisClient.IncrBy(ctx, likeCountKey, 3).Result()
	if err != nil {
		fmt.Printf("❌ 增加点赞失败: %v\n", err)
	} else {
		fmt.Printf("✓ 增加3个赞，当前点赞数: %d\n", newLikes)
	}
	
	// 取消1个赞
	newLikes, err = redisClient.DecrBy(ctx, likeCountKey, 1).Result()
	if err != nil {
		fmt.Printf("❌ 减少点赞失败: %v\n", err)
	} else {
		fmt.Printf("✓ 取消1个赞，当前点赞数: %d\n", newLikes)
	}
	
	fmt.Println("优势：原子性操作，高并发下数据一致性有保障")
}

// 3. 分布式锁场景示例
func distributedLockExample(ctx context.Context) {
	lockKey := "lock:order:payment:12345"
	lockValue := "server-001-" + fmt.Sprintf("%d", time.Now().UnixNano())
	lockTimeout := 10 * time.Second
	
	fmt.Println("🔒 尝试获取分布式锁...")
	
	// 尝试获取锁（SetNX：只有当键不存在时才设置）
	acquired, err := redisClient.SetNX(ctx, lockKey, lockValue, lockTimeout).Result()
	if err != nil {
		fmt.Printf("❌ 获取锁失败: %v\n", err)
		return
	}
	
	if acquired {
		fmt.Printf("✓ 成功获取锁，锁值: %s\n", lockValue)
		fmt.Println("✓ 开始执行业务逻辑（模拟订单支付处理）...")
		
		// 模拟业务处理时间
		time.Sleep(2 * time.Second)
		fmt.Println("✓ 业务逻辑执行完成")
		
		// 释放锁（使用Lua脚本确保原子性）
		luaScript := `
			if redis.call("get", KEYS[1]) == ARGV[1] then
				return redis.call("del", KEYS[1])
			else
				return 0
			end
		`
		result, err := redisClient.Eval(ctx, luaScript, []string{lockKey}, lockValue).Result()
		if err != nil {
			fmt.Printf("❌ 释放锁失败: %v\n", err)
		} else if result.(int64) == 1 {
			fmt.Println("✓ 成功释放锁")
		} else {
			fmt.Println("⚠️ 锁已被其他进程释放或过期")
		}
	} else {
		fmt.Println("❌ 获取锁失败，其他进程正在处理")
	}
	
	fmt.Println("优势：防止并发操作冲突，确保关键业务的串行执行")
}

// 4. 限流场景示例
func rateLimitExample(ctx context.Context) {
	userID := "1001"
	rateLimitKey := fmt.Sprintf("rate_limit:user:%s", userID)
	maxRequests := 5 // 每分钟最多5次请求
	window := 60 * time.Second
	
	fmt.Printf("🚦 用户 %s 的API限流测试（每分钟最多%d次）:\n", userID, maxRequests)
	
	// 模拟连续7次API请求
	for i := 1; i <= 7; i++ {
		// 获取当前请求次数
		currentCount, err := redisClient.Get(ctx, rateLimitKey).Int64()
		if err != nil && err != redis.Nil {
			fmt.Printf("❌ 获取限流计数失败: %v\n", err)
			continue
		}
		
		if currentCount >= int64(maxRequests) {
			fmt.Printf("  请求 #%d: ❌ 被限流，已达到最大请求次数\n", i)
			continue
		}
		
		// 增加请求计数
		newCount, err := redisClient.Incr(ctx, rateLimitKey).Result()
		if err != nil {
			fmt.Printf("❌ 增加限流计数失败: %v\n", err)
			continue
		}
		
		// 如果是第一次请求，设置过期时间
		if newCount == 1 {
			redisClient.Expire(ctx, rateLimitKey, window)
		}
		
		fmt.Printf("  请求 #%d: ✓ 允许通过，当前计数: %d/%d\n", i, newCount, maxRequests)
		
		// 模拟请求间隔
		time.Sleep(200 * time.Millisecond)
	}
	
	// 查看剩余时间
	ttl, _ := redisClient.TTL(ctx, rateLimitKey).Result()
	fmt.Printf("✓ 限流窗口剩余时间: %v\n", ttl)
	
	fmt.Println("优势：有效防止API滥用，保护系统稳定性")
}

// 5. 会话管理场景示例
func sessionManagementExample(ctx context.Context) {
	sessionID := "sess_" + fmt.Sprintf("%d", time.Now().UnixNano())
	sessionKey := "session:" + sessionID
	
	// 用户登录，创建会话
	sessionData := fmt.Sprintf(`{"user_id":1001,"username":"zhangsan","login_time":"%s","role":"user"}`, 
		time.Now().Format("2006-01-02 15:04:05"))
	
	// 设置会话，2小时过期
	err := redisClient.Set(ctx, sessionKey, sessionData, 2*time.Hour).Err()
	if err != nil {
		fmt.Printf("❌ 创建会话失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 用户登录成功，会话ID: %s\n", sessionID)
	fmt.Printf("✓ 会话数据: %s\n", sessionData)
	
	// 模拟用户活动，延长会话
	time.Sleep(1 * time.Second)
	err = redisClient.Expire(ctx, sessionKey, 2*time.Hour).Err()
	if err != nil {
		fmt.Printf("❌ 延长会话失败: %v\n", err)
	} else {
		fmt.Println("✓ 用户活动，会话时间已延长")
	}
	
	// 验证会话
	sessionInfo, err := redisClient.Get(ctx, sessionKey).Result()
	if err != nil {
		fmt.Printf("❌ 会话验证失败: %v\n", err)
	} else {
		fmt.Printf("✓ 会话验证成功: %s\n", sessionInfo)
	}
	
	// 用户登出，删除会话
	deleted, err := redisClient.Del(ctx, sessionKey).Result()
	if err != nil {
		fmt.Printf("❌ 删除会话失败: %v\n", err)
	} else if deleted > 0 {
		fmt.Println("✓ 用户登出，会话已删除")
	}
	
	fmt.Println("优势：快速的会话存取，支持自动过期，减轻服务器内存压力")
}

// 6. 基本操作演示
func basicOperationsExample(ctx context.Context) {
	fmt.Println("📝 基本操作演示:")
	
	// SET/GET
	redisClient.Set(ctx, "demo:key", "Hello Redis", 0)
	value, _ := redisClient.Get(ctx, "demo:key").Result()
	fmt.Printf("✓ SET/GET: %s\n", value)
	
	// SETNX（只有键不存在时才设置）
	success, _ := redisClient.SetNX(ctx, "demo:unique", "first", 0).Result()
	fmt.Printf("✓ SETNX 第一次: %t\n", success)
	success, _ = redisClient.SetNX(ctx, "demo:unique", "second", 0).Result()
	fmt.Printf("✓ SETNX 第二次: %t\n", success)
	
	// GETSET（设置新值并返回旧值）
	oldValue, _ := redisClient.GetSet(ctx, "demo:key", "New Value").Result()
	fmt.Printf("✓ GETSET 旧值: %s\n", oldValue)
	
	// MSET/MGET（批量操作）
	redisClient.MSet(ctx, "key1", "value1", "key2", "value2", "key3", "value3")
	values, _ := redisClient.MGet(ctx, "key1", "key2", "key3").Result()
	fmt.Printf("✓ MGET: %v\n", values)
	
	// 字符串长度
	length, _ := redisClient.StrLen(ctx, "demo:key").Result()
	fmt.Printf("✓ 字符串长度: %d\n", length)
	
	// 追加字符串
	newLength, _ := redisClient.Append(ctx, "demo:key", " Appended").Result()
	fmt.Printf("✓ 追加后长度: %d\n", newLength)
	
	// 获取子字符串
	substring, _ := redisClient.GetRange(ctx, "demo:key", 0, 4).Result()
	fmt.Printf("✓ 子字符串 [0:4]: %s\n", substring)
	
	fmt.Println("✓ 基本操作演示完成")
}

package main

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/go-redis/redis/v8"
)

// 全局 Redis 客户端变量
var redisClient *redis.Client

// 初始化 Redis 客户端连接
func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // Redis 服务器地址
		Password: "",               // Redis 密码（无密码则为空字符串）
		DB:       0,                // 使用的数据库编号，默认0
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis Set 类型详解 ===\n")
	fmt.Println("Set 是 Redis 中的无序、唯一字符串集合，自动去重。")
	fmt.Println("每个 Set 最多可以包含 2^32-1 个元素，支持高效的集合运算操作。")
	fmt.Println("Set 的主要特点：元素唯一性、无序性、支持交集、并集、差集等数学集合操作。")

	// 清理环境
	redisClient.Del(ctx, "tags:user123", "visitors:today", "blacklist:ip", "friends:alice", "friends:bob", 
		"online:users", "skills:developer", "lottery:participants", "permissions:admin")

	// 1. 标签系统场景 - 最典型的Set应用
	fmt.Println("\n1. 标签系统场景:")
	fmt.Println("应用场景：用户标签、商品分类、文章标签、技能标签等")
	tagSystemExample(ctx)

	// 2. 唯一访问者统计场景 - 去重统计
	fmt.Println("\n2. 唯一访问者统计场景:")
	fmt.Println("应用场景：UV统计、去重计数、唯一用户追踪等")
	uniqueVisitorsExample(ctx)

	// 3. 黑名单/白名单场景 - 快速查找
	fmt.Println("\n3. 黑名单/白名单场景:")
	fmt.Println("应用场景：IP黑名单、用户黑名单、权限白名单、垃圾邮件过滤等")
	blacklistExample(ctx)

	// 4. 社交关系场景 - 集合运算
	fmt.Println("\n4. 社交关系场景:")
	fmt.Println("应用场景：好友关系、共同好友、关注列表、推荐系统等")
	socialNetworkExample(ctx)

	// 5. 抽奖系统场景 - 随机选择
	fmt.Println("\n5. 抽奖系统场景:")
	fmt.Println("应用场景：随机抽奖、随机推荐、随机选择等")
	lotterySystemExample(ctx)

	// 6. 基本操作演示
	fmt.Println("\n6. 基本操作演示:")
	basicSetOperationsExample(ctx)
}

// 1. 标签系统场景
func tagSystemExample(ctx context.Context) {
	userTagKey := "tags:user123"
	
	fmt.Println("🏷️ 用户标签管理:")
	
	// 用户添加技能标签
	skillTags := []string{"Go", "Redis", "Docker", "Kubernetes", "MySQL", "JavaScript"}
	
	fmt.Println("📝 添加技能标签:")
	for i, tag := range skillTags {
		added, err := redisClient.SAdd(ctx, userTagKey, tag).Result()
		if err != nil {
			fmt.Printf("❌ 添加标签失败: %v\n", err)
			continue
		}
		if added > 0 {
			fmt.Printf("  ✓ 添加标签: %s\n", tag)
		} else {
			fmt.Printf("  - 标签已存在: %s\n", tag)
		}
	}
	
	// 尝试添加重复标签
	fmt.Println("\n🔄 尝试添加重复标签:")
	duplicateAdded, err := redisClient.SAdd(ctx, userTagKey, "Go", "Python").Result()
	if err != nil {
		fmt.Printf("❌ 添加重复标签失败: %v\n", err)
	} else {
		fmt.Printf("✓ 新增了 %d 个标签 (Go重复，Python新增)\n", duplicateAdded)
	}
	
	// 获取所有标签
	fmt.Println("\n📋 用户的所有标签:")
	allTags, err := redisClient.SMembers(ctx, userTagKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取标签失败: %v\n", err)
	} else {
		for i, tag := range allTags {
			fmt.Printf("  %d. %s\n", i+1, tag)
		}
	}
	
	// 检查特定标签是否存在
	checkTags := []string{"Redis", "Python", "Java"}
	fmt.Println("\n🔍 检查标签是否存在:")
	for _, tag := range checkTags {
		exists, err := redisClient.SIsMember(ctx, userTagKey, tag).Result()
		if err != nil {
			fmt.Printf("❌ 检查标签失败: %v\n", err)
			continue
		}
		status := "❌"
		if exists {
			status = "✅"
		}
		fmt.Printf("  %s %s\n", status, tag)
	}
	
	// 获取标签数量
	tagCount, err := redisClient.SCard(ctx, userTagKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取标签数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 用户总标签数: %d\n", tagCount)
	}
	
	// 随机获取几个标签（用于推荐展示）
	randomTags, err := redisClient.SRandMember(ctx, userTagKey, 3).Result()
	if err != nil {
		fmt.Printf("❌ 获取随机标签失败: %v\n", err)
	} else {
		fmt.Printf("✓ 随机展示标签: %v\n", randomTags)
	}
	
	fmt.Println("优势：自动去重，快速查找，支持随机选择")
}

// 2. 唯一访问者统计场景
func uniqueVisitorsExample(ctx context.Context) {
	visitorsKey := "visitors:today"
	
	fmt.Println("👥 今日唯一访问者统计:")
	
	// 模拟用户访问（包含重复访问）
	visitors := []string{
		"*************", "*************", "*************",
		"*************", // 重复访问
		"*************", "*************",
		"*************", // 重复访问
		"*************", "*************",
		"*************", // 重复访问
	}
	
	fmt.Println("📊 记录访问者IP:")
	totalRequests := 0
	for i, ip := range visitors {
		added, err := redisClient.SAdd(ctx, visitorsKey, ip).Result()
		if err != nil {
			fmt.Printf("❌ 记录访问失败: %v\n", err)
			continue
		}
		totalRequests++
		
		if added > 0 {
			fmt.Printf("  请求 #%d: %s (新访问者)\n", i+1, ip)
		} else {
			fmt.Printf("  请求 #%d: %s (重复访问)\n", i+1, ip)
		}
	}
	
	// 获取今日唯一访问者数量
	uniqueCount, err := redisClient.SCard(ctx, visitorsKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取访问者数量失败: %v\n", err)
	} else {
		fmt.Printf("\n📈 统计结果:\n")
		fmt.Printf("  总请求数: %d\n", totalRequests)
		fmt.Printf("  唯一访问者: %d\n", uniqueCount)
		fmt.Printf("  重复访问: %d\n", totalRequests-int(uniqueCount))
	}
	
	// 获取所有唯一访问者
	fmt.Println("\n📋 今日所有唯一访问者:")
	allVisitors, err := redisClient.SMembers(ctx, visitorsKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取访问者列表失败: %v\n", err)
	} else {
		for i, visitor := range allVisitors {
			fmt.Printf("  %d. %s\n", i+1, visitor)
		}
	}
	
	// 检查特定IP是否访问过
	checkIPs := []string{"*************", "*************"}
	fmt.Println("\n🔍 检查特定IP访问情况:")
	for _, ip := range checkIPs {
		visited, err := redisClient.SIsMember(ctx, visitorsKey, ip).Result()
		if err != nil {
			fmt.Printf("❌ 检查访问失败: %v\n", err)
			continue
		}
		status := "未访问"
		if visited {
			status = "已访问"
		}
		fmt.Printf("  %s: %s\n", ip, status)
	}
	
	fmt.Println("优势：自动去重统计，O(1)时间复杂度查找，内存效率高")
}

// 3. 黑名单/白名单场景
func blacklistExample(ctx context.Context) {
	blacklistKey := "blacklist:ip"
	
	fmt.Println("🚫 IP黑名单管理:")
	
	// 添加恶意IP到黑名单
	maliciousIPs := []string{
		"*************", "*********", "************",
		"************", "*************",
	}
	
	fmt.Println("📝 添加恶意IP到黑名单:")
	for i, ip := range maliciousIPs {
		added, err := redisClient.SAdd(ctx, blacklistKey, ip).Result()
		if err != nil {
			fmt.Printf("❌ 添加黑名单失败: %v\n", err)
			continue
		}
		if added > 0 {
			fmt.Printf("  %d. ✓ 已拉黑: %s\n", i+1, ip)
		}
	}
	
	// 模拟请求检查
	testIPs := []string{
		"*************", // 正常IP
		"*************", // 黑名单IP
		"*********",     // 黑名单IP
		"*************", // 正常IP
	}
	
	fmt.Println("\n🔍 请求IP检查:")
	for i, ip := range testIPs {
		isBlocked, err := redisClient.SIsMember(ctx, blacklistKey, ip).Result()
		if err != nil {
			fmt.Printf("❌ 检查黑名单失败: %v\n", err)
			continue
		}
		
		if isBlocked {
			fmt.Printf("  请求 #%d: %s ❌ 被拦截 (黑名单)\n", i+1, ip)
		} else {
			fmt.Printf("  请求 #%d: %s ✅ 允许通过\n", i+1, ip)
		}
	}
	
	// 获取黑名单总数
	blacklistCount, err := redisClient.SCard(ctx, blacklistKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取黑名单数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 黑名单IP总数: %d\n", blacklistCount)
	}
	
	// 从黑名单中移除IP（误判处理）
	removeIP := "*************"
	removed, err := redisClient.SRem(ctx, blacklistKey, removeIP).Result()
	if err != nil {
		fmt.Printf("❌ 移除黑名单失败: %v\n", err)
	} else {
		if removed > 0 {
			fmt.Printf("✓ 已从黑名单移除: %s\n", removeIP)
		} else {
			fmt.Printf("⚠️ IP不在黑名单中: %s\n", removeIP)
		}
	}
	
	// 获取当前黑名单
	fmt.Println("\n📋 当前黑名单:")
	currentBlacklist, err := redisClient.SMembers(ctx, blacklistKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取黑名单失败: %v\n", err)
	} else {
		for i, ip := range currentBlacklist {
			fmt.Printf("  %d. %s\n", i+1, ip)
		}
	}
	
	fmt.Println("优势：O(1)时间复杂度查找，高效的访问控制，支持动态管理")
}

// 4. 社交关系场景
func socialNetworkExample(ctx context.Context) {
	aliceFriendsKey := "friends:alice"
	bobFriendsKey := "friends:bob"
	
	fmt.Println("👫 社交网络好友关系:")
	
	// Alice的好友列表
	aliceFriends := []string{"charlie", "david", "eve", "frank", "grace"}
	// Bob的好友列表
	bobFriends := []string{"david", "eve", "henry", "iris", "jack"}
	
	// 添加Alice的好友
	fmt.Println("📝 Alice的好友:")
	err := redisClient.SAdd(ctx, aliceFriendsKey, 
		redis.Args{}.AddFlat(aliceFriends)...).Err()
	if err != nil {
		fmt.Printf("❌ 添加Alice好友失败: %v\n", err)
	} else {
		for i, friend := range aliceFriends {
			fmt.Printf("  %d. %s\n", i+1, friend)
		}
	}
	
	// 添加Bob的好友
	fmt.Println("\n📝 Bob的好友:")
	err = redisClient.SAdd(ctx, bobFriendsKey, 
		redis.Args{}.AddFlat(bobFriends)...).Err()
	if err != nil {
		fmt.Printf("❌ 添加Bob好友失败: %v\n", err)
	} else {
		for i, friend := range bobFriends {
			fmt.Printf("  %d. %s\n", i+1, friend)
		}
	}
	
	// 查找共同好友（交集）
	fmt.Println("\n🤝 Alice和Bob的共同好友:")
	commonFriends, err := redisClient.SInter(ctx, aliceFriendsKey, bobFriendsKey).Result()
	if err != nil {
		fmt.Printf("❌ 查找共同好友失败: %v\n", err)
	} else {
		if len(commonFriends) == 0 {
			fmt.Println("  没有共同好友")
		} else {
			for i, friend := range commonFriends {
				fmt.Printf("  %d. %s\n", i+1, friend)
			}
		}
	}
	
	// 查找Alice独有的好友（差集）
	fmt.Println("\n👤 Alice独有的好友 (Alice有但Bob没有):")
	aliceOnlyFriends, err := redisClient.SDiff(ctx, aliceFriendsKey, bobFriendsKey).Result()
	if err != nil {
		fmt.Printf("❌ 查找Alice独有好友失败: %v\n", err)
	} else {
		for i, friend := range aliceOnlyFriends {
			fmt.Printf("  %d. %s\n", i+1, friend)
		}
	}
	
	// 查找所有好友（并集）
	fmt.Println("\n👥 Alice和Bob的所有好友 (并集):")
	allFriends, err := redisClient.SUnion(ctx, aliceFriendsKey, bobFriendsKey).Result()
	if err != nil {
		fmt.Printf("❌ 查找所有好友失败: %v\n", err)
	} else {
		fmt.Printf("  总共 %d 个不同的好友:\n", len(allFriends))
		for i, friend := range allFriends {
			fmt.Printf("  %d. %s\n", i+1, friend)
		}
	}
	
	// 好友推荐（Bob的好友中Alice没有的）
	fmt.Println("\n💡 为Alice推荐好友 (Bob的好友中Alice没有的):")
	recommendedFriends, err := redisClient.SDiff(ctx, bobFriendsKey, aliceFriendsKey).Result()
	if err != nil {
		fmt.Printf("❌ 生成推荐失败: %v\n", err)
	} else {
		if len(recommendedFriends) == 0 {
			fmt.Println("  暂无推荐好友")
		} else {
			for i, friend := range recommendedFriends {
				fmt.Printf("  推荐 %d: %s (通过Bob认识)\n", i+1, friend)
			}
		}
	}
	
	fmt.Println("优势：高效的集合运算，支持复杂的社交关系分析，推荐算法基础")
}

// 5. 抽奖系统场景
func lotterySystemExample(ctx context.Context) {
	participantsKey := "lottery:participants"
	
	fmt.Println("🎲 抽奖系统:")
	
	// 用户报名参加抽奖
	participants := []string{
		"user001", "user002", "user003", "user004", "user005",
		"user006", "user007", "user008", "user009", "user010",
	}
	
	fmt.Println("📝 用户报名抽奖:")
	for i, user := range participants {
		added, err := redisClient.SAdd(ctx, participantsKey, user).Result()
		if err != nil {
			fmt.Printf("❌ 用户报名失败: %v\n", err)
			continue
		}
		if added > 0 {
			fmt.Printf("  %d. ✓ %s 报名成功\n", i+1, user)
		}
	}
	
	// 模拟重复报名
	fmt.Println("\n🔄 处理重复报名:")
	duplicateUser := "user005"
	added, err := redisClient.SAdd(ctx, participantsKey, duplicateUser).Result()
	if err != nil {
		fmt.Printf("❌ 处理重复报名失败: %v\n", err)
	} else {
		if added == 0 {
			fmt.Printf("⚠️ %s 已经报名，不能重复参与\n", duplicateUser)
		}
	}
	
	// 获取参与人数
	totalParticipants, err := redisClient.SCard(ctx, participantsKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取参与人数失败: %v\n", err)
	} else {
		fmt.Printf("✓ 总参与人数: %d\n", totalParticipants)
	}
	
	// 随机抽取一等奖（1名）
	fmt.Println("\n🏆 一等奖抽奖:")
	firstPrizeWinner, err := redisClient.SPop(ctx, participantsKey).Result()
	if err != nil {
		fmt.Printf("❌ 抽取一等奖失败: %v\n", err)
	} else {
		fmt.Printf("🎉 一等奖获得者: %s\n", firstPrizeWinner)
	}
	
	// 随机抽取二等奖（2名）
	fmt.Println("\n🥈 二等奖抽奖:")
	secondPrizeWinners, err := redisClient.SPopN(ctx, participantsKey, 2).Result()
	if err != nil {
		fmt.Printf("❌ 抽取二等奖失败: %v\n", err)
	} else {
		for i, winner := range secondPrizeWinners {
			fmt.Printf("🎉 二等奖获得者 %d: %s\n", i+1, winner)
		}
	}
	
	// 随机预览三等奖候选人（不移除）
	fmt.Println("\n🥉 三等奖候选人预览:")
	thirdPrizeCandidates, err := redisClient.SRandMember(ctx, participantsKey, 3).Result()
	if err != nil {
		fmt.Printf("❌ 预览候选人失败: %v\n", err)
	} else {
		for i, candidate := range thirdPrizeCandidates {
			fmt.Printf("  候选人 %d: %s\n", i+1, candidate)
		}
	}
	
	// 查看剩余参与者
	remainingParticipants, err := redisClient.SMembers(ctx, participantsKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取剩余参与者失败: %v\n", err)
	} else {
		fmt.Printf("✓ 剩余参与者数量: %d\n", len(remainingParticipants))
		fmt.Printf("  剩余参与者: %v\n", remainingParticipants)
	}
	
	// 模拟抽奖结束，清空参与者
	fmt.Println("\n🔚 抽奖结束，清理数据:")
	cleared, err := redisClient.Del(ctx, participantsKey).Result()
	if err != nil {
		fmt.Printf("❌ 清理数据失败: %v\n", err)
	} else {
		fmt.Printf("✓ 已清理 %d 个抽奖数据\n", cleared)
	}
	
	fmt.Println("优势：真正的随机选择，防止重复参与，支持批量抽取")
}

// 6. 基本操作演示
func basicSetOperationsExample(ctx context.Context) {
	demoKey1 := "demo:set1"
	demoKey2 := "demo:set2"
	
	fmt.Println("🔧 Set基本操作演示:")
	
	// 清理
	redisClient.Del(ctx, demoKey1, demoKey2)
	
	// SADD - 添加元素
	redisClient.SAdd(ctx, demoKey1, "a", "b", "c", "d")
	redisClient.SAdd(ctx, demoKey2, "c", "d", "e", "f")
	fmt.Println("✓ SADD: 添加元素到两个集合")
	
	// SMEMBERS - 获取所有元素
	members1, _ := redisClient.SMembers(ctx, demoKey1).Result()
	members2, _ := redisClient.SMembers(ctx, demoKey2).Result()
	fmt.Printf("✓ SMEMBERS set1: %v\n", members1)
	fmt.Printf("✓ SMEMBERS set2: %v\n", members2)
	
	// SCARD - 获取元素数量
	count1, _ := redisClient.SCard(ctx, demoKey1).Result()
	count2, _ := redisClient.SCard(ctx, demoKey2).Result()
	fmt.Printf("✓ SCARD set1: %d, set2: %d\n", count1, count2)
	
	// SISMEMBER - 检查元素是否存在
	exists, _ := redisClient.SIsMember(ctx, demoKey1, "b").Result()
	fmt.Printf("✓ SISMEMBER set1 'b': %t\n", exists)
	
	// SINTER - 交集
	intersection, _ := redisClient.SInter(ctx, demoKey1, demoKey2).Result()
	fmt.Printf("✓ SINTER: %v\n", intersection)
	
	// SUNION - 并集
	union, _ := redisClient.SUnion(ctx, demoKey1, demoKey2).Result()
	fmt.Printf("✓ SUNION: %v\n", union)
	
	// SDIFF - 差集
	difference, _ := redisClient.SDiff(ctx, demoKey1, demoKey2).Result()
	fmt.Printf("✓ SDIFF set1-set2: %v\n", difference)
	
	// SRANDMEMBER - 随机获取元素
	randomMember, _ := redisClient.SRandMember(ctx, demoKey1).Result()
	fmt.Printf("✓ SRANDMEMBER: %s\n", randomMember)
	
	// SPOP - 随机移除元素
	poppedMember, _ := redisClient.SPop(ctx, demoKey1).Result()
	fmt.Printf("✓ SPOP: %s\n", poppedMember)
	
	// SREM - 移除指定元素
	removed, _ := redisClient.SRem(ctx, demoKey1, "b").Result()
	fmt.Printf("✓ SREM 'b': %d个元素被移除\n", removed)
	
	// 最终状态
	finalMembers, _ := redisClient.SMembers(ctx, demoKey1).Result()
	fmt.Printf("✓ 最终 set1: %v\n", finalMembers)
	
	fmt.Println("✓ Set基本操作演示完成")
}

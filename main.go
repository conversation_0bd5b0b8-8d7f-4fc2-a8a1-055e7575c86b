package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// 全局 Redis 客户端变量
var redisClient *redis.Client

// 初始化 Redis 客户端连接
func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // Redis 服务器地址
		Password: "",               // Redis 密码（无密码则为空字符串）
		DB:       0,                // 使用的数据库编号，默认0
	})
}

func main() {
	// 创建一个上下文对象，用于 Redis 操作
	ctx := context.Background()

	fmt.Println("=== Redis String 类型操作示例 ===\n")

	// 1. 基本的 Set 和 Get 操作演示
	fmt.Println("1. 基本 Set/Get 操作:")
	setAndGetExample(ctx)

	// 2. GetSet 操作 - 设置新值并返回旧值
	fmt.Println("\n2. GetSet 操作:")
	getSetExample(ctx)

	// 3. SetNX 操作 - 只在键不存在时设置
	fmt.Println("\n3. SetNX 操作:")
	setNXExample(ctx)

	// 4. 批量操作 MGet 和 MSet
	fmt.Println("\n4. 批量操作 MGet/MSet:")
	batchExample(ctx)

	// 5. 数字操作 - 计数器
	fmt.Println("\n5. 数字操作 - 计数器:")
	counterExample(ctx)

	// 6. 过期时间设置
	fmt.Println("\n6. 过期时间设置:")
	expireExample(ctx)
}

// 1. 基本的 Set 和 Get 操作
func setAndGetExample(ctx context.Context) {
	// 向 Redis 设置键 user:name 的值为 "张三"，0 表示不过期
	err := redisClient.Set(ctx, "user:name", "张三", 0).Err()
	if err != nil {
		fmt.Printf("设置失败: %v\n", err)
		return
	}
	fmt.Println("✓ 设置 user:name = 张三")

	// 获取键 user:name 的值
	value, err := redisClient.Get(ctx, "user:name").Result()
	if err != nil {
		fmt.Printf("获取失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 获取 user:name = %s\n", value)
}

// 2. GetSet 操作 - 设置新值并返回旧值
func getSetExample(ctx context.Context) {
	// 先设置一个初始值，方便后续 GetSet 操作
	redisClient.Set(ctx, "counter", "100", 0)

	// GetSet 操作：将 counter 的值设置为 "200"，并返回旧值
	oldValue, err := redisClient.GetSet(ctx, "counter", "200").Result()
	if err != nil {
		fmt.Printf("GetSet 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 旧值: %s, 新值: 200\n", oldValue)

	// 再次获取 counter，验证新值是否生效
	newValue, _ := redisClient.Get(ctx, "counter").Result()
	fmt.Printf("✓ 验证新值: %s\n", newValue)
}

// 3. SetNX 操作 - 只在键不存在时设置
func setNXExample(ctx context.Context) {
	// 先删除 unique_key，确保测试环境干净
	redisClient.Del(ctx, "unique_key")

	// 第一次设置 unique_key，应该会成功（因为键不存在）
	success, err := redisClient.SetNX(ctx, "unique_key", "第一次设置", 0).Result()
	if err != nil {
		fmt.Printf("SetNX 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 第一次 SetNX: %t\n", success)

	// 第二次设置 unique_key，应该会失败（因为键已存在）
	success, err = redisClient.SetNX(ctx, "unique_key", "第二次设置", 0).Result()
	if err != nil {
		fmt.Printf("SetNX 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 第二次 SetNX: %t\n", success)

	// 获取 unique_key 的实际值，验证是否为第一次设置的值
	value, _ := redisClient.Get(ctx, "unique_key").Result()
	fmt.Printf("✓ 实际值: %s\n", value)
}

// 4. 批量操作 MGet 和 MSet
func batchExample(ctx context.Context) {
	// 批量设置多个键值对，使用 map 传递
	err := redisClient.MSet(ctx, map[string]any{
		"user:1:name": "李四", // 用户1的姓名
		"user:1:age":  "25", // 用户1的年龄
		"user:1:city": "北京", // 用户1的城市
	}).Err()
	if err != nil {
		fmt.Printf("MSet 失败: %v\n", err)
		return
	}
	fmt.Println("✓ 批量设置完成")

	// 批量获取多个键的值，返回值为 []interface{}
	values, err := redisClient.MGet(ctx, "user:1:name", "user:1:age", "user:1:city").Result()
	if err != nil {
		fmt.Printf("MGet 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 批量获取: name=%s, age=%s, city=%s\n", values[0], values[1], values[2])
}

// 5. 数字操作 - 计数器
func counterExample(ctx context.Context) {
	// 删除可能存在的计数器，确保测试环境干净
	redisClient.Del(ctx, "visit_count")

	// 初始化计数器 visit_count 为 0
	redisClient.Set(ctx, "visit_count", "0", 0)

	// 连续访问3次，每次自增1
	for i := 1; i <= 3; i++ {
		newCount, err := redisClient.Incr(ctx, "visit_count").Result()
		if err != nil {
			fmt.Printf("Incr 失败: %v\n", err)
			return
		}
		fmt.Printf("✓ 第 %d 次访问，计数: %d\n", i, newCount)
	}

	// 计数器自减1
	newCount, err := redisClient.Decr(ctx, "visit_count").Result()
	if err != nil {
		fmt.Printf("Decr 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 减少一次，计数: %d\n", newCount)

	// 计数器增加指定数值（如10）
	newCount, err = redisClient.IncrBy(ctx, "visit_count", 10).Result()
	if err != nil {
		fmt.Printf("IncrBy 失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 增加 10，计数: %d\n", newCount)
}

// 6. 过期时间设置
func expireExample(ctx context.Context) {
	// 设置一个键 temp_key，值为"临时数据"，5秒后过期
	err := redisClient.Set(ctx, "temp_key", "临时数据", 5*time.Second).Err()
	if err != nil {
		fmt.Printf("设置过期键失败: %v\n", err)
		return
	}
	fmt.Println("✓ 设置 temp_key，5秒后过期")

	// 立即获取 temp_key 的值，验证是否设置成功
	value, _ := redisClient.Get(ctx, "temp_key").Result()
	fmt.Printf("✓ 立即获取: %s\n", value)

	// 查询 temp_key 剩余的生存时间（TTL）
	ttl, _ := redisClient.TTL(ctx, "temp_key").Result()
	fmt.Printf("✓ 剩余生存时间: %v\n", ttl)

	// 等待6秒，确保键已经过期
	fmt.Println("等待 6 秒...")
	time.Sleep(6 * time.Second)

	// 再次获取 temp_key，应该已经过期，获取会报错
	value, err = redisClient.Get(ctx, "temp_key").Result()
	if err != nil {
		fmt.Printf("✓ 6秒后获取失败（已过期）: %v\n", err)
	} else {
		fmt.Printf("✓ 6秒后获取: %s\n", value)
	}
}

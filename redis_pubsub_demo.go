package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

var redisClient *redis.Client

func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis 发布订阅模式示例 ===\n")

	// 创建一个通道来控制程序退出
	done := make(chan struct{})

	// 1. 启动订阅者
	go startSubscriber(ctx, done)

	// 等待订阅者准备就绪
	time.Sleep(1 * time.Second)

	// 2. 发布消息
	publishMessages(ctx)

	// 等待一段时间，确保消息被处理
	time.Sleep(2 * time.Second)

	// 3. 发布退出信号
	fmt.Println("\n✓ 发布退出信号...")
	redisClient.Publish(ctx, "news", "EXIT")

	// 等待订阅者退出
	<-done
	fmt.Println("✓ 程序执行完毕")
}

// 启动订阅者
func startSubscriber(ctx context.Context, done chan struct{}) {
	fmt.Println("1. 启动订阅者:")
	fmt.Println("✓ 订阅者开始监听 'news' 频道...")

	// 订阅 "news" 频道
	pubsub := redisClient.Subscribe(ctx, "news")
	defer pubsub.Close()

	// 接收订阅确认消息
	_, err := pubsub.Receive(ctx)
	if err != nil {
		fmt.Printf("❌ 订阅失败: %v\n", err)
		close(done)
		return
	}

	// 获取消息通道
	ch := pubsub.Channel()

	// 监听消息
	for msg := range ch {
		// 检查是否为退出信号
		if msg.Payload == "EXIT" {
			fmt.Println("✓ 订阅者收到退出信号，准备退出...")
			break
		}

		// 处理接收到的消息
		fmt.Printf("✓ 订阅者收到消息: %s\n", msg.Payload)
	}

	close(done)
}

// 发布消息
func publishMessages(ctx context.Context) {
	fmt.Println("\n2. 发布消息:")

	// 发布一系列消息
	messages := []string{
		"今日头条：新技术突破！",
		"体育新闻：某队获得冠军！",
		"财经动态：股市上涨3%！",
	}

	for i, msg := range messages {
		// 发布消息到 "news" 频道
		numReceivers, err := redisClient.Publish(ctx, "news", msg).Result()
		if err != nil {
			fmt.Printf("❌ 消息发布失败: %v\n", err)
			continue
		}

		fmt.Printf("✓ 发布消息 #%d: \"%s\" (收到者数量: %d)\n", i+1, msg, numReceivers)

		// 短暂暂停，便于观察
		time.Sleep(500 * time.Millisecond)
	}
}

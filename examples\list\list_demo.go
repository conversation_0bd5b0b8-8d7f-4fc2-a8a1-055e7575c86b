package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

var redisClient *redis.Client

func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis List 类型详解 ===\n")
	fmt.Println("List 是 Redis 中的有序字符串列表，支持双向操作，类似于链表结构。")

	// 清理环境
	redisClient.Del(ctx, "queue:tasks", "timeline:user123", "logs:system", "notifications:user456", "history:actions")

	// 1. 消息队列场景
	fmt.Println("\n1. 消息队列场景:")
	messageQueueExample(ctx)

	// 2. 社交网络时间线场景
	fmt.Println("\n2. 社交网络时间线场景:")
	timelineExample(ctx)

	// 3. 日志系统场景
	fmt.Println("\n3. 日志系统场景:")
	loggingSystemExample(ctx)

	// 4. 通知系统场景
	fmt.Println("\n4. 通知系统场景:")
	notificationSystemExample(ctx)

	// 5. 操作历史场景
	fmt.Println("\n5. 操作历史场景:")
	historyTrackingExample(ctx)
}

// 1. 消息队列场景
func messageQueueExample(ctx context.Context) {
	queueKey := "queue:tasks"

	// 生产者：向队列添加任务
	tasks := []string{
		"发送邮件给用户123",
		"处理订单456",
		"生成报表789",
		"备份数据库",
		"清理临时文件",
	}

	fmt.Println("✓ 生产者开始添加任务到队列:")
	for _, task := range tasks {
		// 使用 LPush 从左侧添加任务（队列头部）
		listLen, err := redisClient.LPush(ctx, queueKey, task).Result()
		if err != nil {
			fmt.Printf("添加任务失败: %v\n", err)
			continue
		}
		fmt.Printf("  - 添加任务: %s (队列长度: %d)\n", task, listLen)
	}

	// 查看队列长度
	queueLen, err := redisClient.LLen(ctx, queueKey).Result()
	if err != nil {
		fmt.Printf("获取队列长度失败: %v\n", err)
	} else {
		fmt.Printf("✓ 当前队列长度: %d\n", queueLen)
	}

	// 消费者：从队列获取并处理任务
	fmt.Println("\n✓ 消费者开始处理任务:")
	for i := 0; i < 3; i++ {
		// 使用 RPop 从右侧获取任务（队列尾部）
		task, err := redisClient.RPop(ctx, queueKey).Result()
		if err != nil {
			if err == redis.Nil {
				fmt.Println("  - 队列为空，没有任务")
			} else {
				fmt.Printf("获取任务失败: %v\n", err)
			}
			break
		}

		fmt.Printf("  - 处理任务: %s\n", task)
		// 模拟任务处理时间
		time.Sleep(100 * time.Millisecond)
	}

	// 查看剩余任务
	remainingTasks, err := redisClient.LRange(ctx, queueKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("获取剩余任务失败: %v\n", err)
	} else {
		fmt.Printf("✓ 剩余任务: %v\n", remainingTasks)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 异步任务队列")
	fmt.Println("- 消息中间件")
	fmt.Println("- 生产者-消费者模式")
	fmt.Println("- 批处理任务管理")
}

// 2. 社交网络时间线场景
func timelineExample(ctx context.Context) {
	timelineKey := "timeline:user123"

	// 模拟用户发布动态
	posts := []string{
		"刚刚看了一部很棒的电影！",
		"今天天气真好，适合出门散步",
		"分享一张美食照片",
		"工作中学到了新技术",
		"周末计划去爬山",
	}

	fmt.Println("✓ 用户发布动态:")
	for i, post := range posts {
		// 使用 LPush 将最新动态添加到列表头部
		timestamp := time.Now().Add(time.Duration(-i) * time.Hour).Format("2006-01-02 15:04:05")
		postWithTime := fmt.Sprintf("[%s] %s", timestamp, post)

		err := redisClient.LPush(ctx, timelineKey, postWithTime).Err()
		if err != nil {
			fmt.Printf("发布动态失败: %v\n", err)
			continue
		}
		fmt.Printf("  - %s\n", postWithTime)
	}

	// 获取最新的3条动态
	fmt.Println("\n✓ 最新的3条动态:")
	recentPosts, err := redisClient.LRange(ctx, timelineKey, 0, 2).Result()
	if err != nil {
		fmt.Printf("获取动态失败: %v\n", err)
	} else {
		for i, post := range recentPosts {
			fmt.Printf("  %d. %s\n", i+1, post)
		}
	}

	// 获取所有动态
	fmt.Println("\n✓ 所有动态:")
	allPosts, err := redisClient.LRange(ctx, timelineKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("获取所有动态失败: %v\n", err)
	} else {
		for i, post := range allPosts {
			fmt.Printf("  %d. %s\n", i+1, post)
		}
	}

	// 限制时间线长度（只保留最新的10条）
	err = redisClient.LTrim(ctx, timelineKey, 0, 9).Err()
	if err != nil {
		fmt.Printf("修剪时间线失败: %v\n", err)
	} else {
		fmt.Println("✓ 时间线已修剪，只保留最新的10条动态")
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 社交媒体时间线")
	fmt.Println("- 新闻feed流")
	fmt.Println("- 活动动态展示")
	fmt.Println("- 最新内容推送")
}

// 3. 日志系统场景
func loggingSystemExample(ctx context.Context) {
	logKey := "logs:system"

	// 模拟系统日志
	logEntries := []struct {
		level   string
		message string
	}{
		{"INFO", "系统启动完成"},
		{"DEBUG", "连接数据库成功"},
		{"WARN", "内存使用率达到80%"},
		{"ERROR", "API请求超时"},
		{"INFO", "定时任务执行完成"},
	}

	fmt.Println("✓ 系统日志记录:")
	for _, entry := range logEntries {
		timestamp := time.Now().Format("2006-01-02 15:04:05.000")
		logLine := fmt.Sprintf("[%s] [%s] %s", timestamp, entry.level, entry.message)

		// 使用 RPush 将日志添加到列表尾部（按时间顺序）
		err := redisClient.RPush(ctx, logKey, logLine).Err()
		if err != nil {
			fmt.Printf("记录日志失败: %v\n", err)
			continue
		}
		fmt.Printf("  - %s\n", logLine)

		// 模拟时间间隔
		time.Sleep(50 * time.Millisecond)
	}

	// 获取最近的日志
	fmt.Println("\n✓ 最近3条日志:")
	recentLogs, err := redisClient.LRange(ctx, logKey, -3, -1).Result()
	if err != nil {
		fmt.Printf("获取日志失败: %v\n", err)
	} else {
		for _, log := range recentLogs {
			fmt.Printf("  - %s\n", log)
		}
	}

	// 根据索引获取特定日志
	specificLog, err := redisClient.LIndex(ctx, logKey, 2).Result()
	if err != nil {
		fmt.Printf("获取特定日志失败: %v\n", err)
	} else {
		fmt.Printf("✓ 第3条日志: %s\n", specificLog)
	}

	// 获取日志总数
	logCount, err := redisClient.LLen(ctx, logKey).Result()
	if err != nil {
		fmt.Printf("获取日志数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 总日志数量: %d\n", logCount)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 系统日志收集")
	fmt.Println("- 应用程序日志")
	fmt.Println("- 审计日志")
	fmt.Println("- 错误日志追踪")
}

// 4. 通知系统场景
func notificationSystemExample(ctx context.Context) {
	notificationKey := "notifications:user456"

	// 清空通知
	redisClient.Del(ctx, notificationKey)

	// 添加各种类型的通知
	notifications := []string{
		"系统维护通知：今晚23:00-01:00进行系统维护",
		"订单通知：您的订单#12345已发货",
		"消息通知：张三给您发送了一条消息",
		"活动通知：双11活动即将开始",
		"安全通知：检测到异常登录，请确认",
	}

	fmt.Println("✓ 发送通知:")
	for _, notification := range notifications {
		timestamp := time.Now().Format("15:04:05")
		notificationWithTime := fmt.Sprintf("[%s] %s", timestamp, notification)

		// 使用 LPush 将最新通知添加到头部
		err := redisClient.LPush(ctx, notificationKey, notificationWithTime).Err()
		if err != nil {
			fmt.Printf("发送通知失败: %v\n", err)
			continue
		}
		fmt.Printf("  - %s\n", notificationWithTime)
		time.Sleep(200 * time.Millisecond)
	}

	// 用户查看通知（获取最新的3条）
	fmt.Println("\n✓ 用户查看最新通知:")
	latestNotifications, err := redisClient.LRange(ctx, notificationKey, 0, 2).Result()
	if err != nil {
		fmt.Printf("获取通知失败: %v\n", err)
	} else {
		for i, notification := range latestNotifications {
			fmt.Printf("  %d. %s\n", i+1, notification)
		}
	}

	// 标记通知为已读（删除第一条通知）
	readNotification, err := redisClient.LPop(ctx, notificationKey).Result()
	if err != nil {
		fmt.Printf("标记通知已读失败: %v\n", err)
	} else {
		fmt.Printf("✓ 已读通知: %s\n", readNotification)
	}

	// 获取未读通知数量
	unreadCount, err := redisClient.LLen(ctx, notificationKey).Result()
	if err != nil {
		fmt.Printf("获取未读数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 未读通知数量: %d\n", unreadCount)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 用户通知中心")
	fmt.Println("- 系统消息推送")
	fmt.Println("- 实时提醒功能")
	fmt.Println("- 消息队列管理")
}

// 5. 操作历史场景
func historyTrackingExample(ctx context.Context) {
	historyKey := "history:actions"

	// 模拟用户操作历史
	actions := []string{
		"用户登录",
		"查看商品列表",
		"添加商品到购物车",
		"修改购物车数量",
		"进入结算页面",
		"填写收货地址",
		"选择支付方式",
		"提交订单",
	}

	fmt.Println("✓ 记录用户操作历史:")
	for _, action := range actions {
		timestamp := time.Now().Format("15:04:05")
		actionWithTime := fmt.Sprintf("[%s] %s", timestamp, action)

		// 使用 RPush 按时间顺序记录操作
		err := redisClient.RPush(ctx, historyKey, actionWithTime).Err()
		if err != nil {
			fmt.Printf("记录操作失败: %v\n", err)
			continue
		}
		fmt.Printf("  - %s\n", actionWithTime)
		time.Sleep(100 * time.Millisecond)
	}

	// 获取完整操作历史
	fmt.Println("\n✓ 完整操作历史:")
	allHistory, err := redisClient.LRange(ctx, historyKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("获取历史失败: %v\n", err)
	} else {
		for i, action := range allHistory {
			fmt.Printf("  %d. %s\n", i+1, action)
		}
	}

	// 获取最近5个操作
	fmt.Println("\n✓ 最近5个操作:")
	recentActions, err := redisClient.LRange(ctx, historyKey, -5, -1).Result()
	if err != nil {
		fmt.Printf("获取最近操作失败: %v\n", err)
	} else {
		for i, action := range recentActions {
			fmt.Printf("  %d. %s\n", i+1, action)
		}
	}

	// 限制历史记录长度（只保留最近50条）
	err = redisClient.LTrim(ctx, historyKey, -50, -1).Err()
	if err != nil {
		fmt.Printf("修剪历史记录失败: %v\n", err)
	} else {
		fmt.Println("✓ 历史记录已修剪，只保留最近50条")
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 用户操作历史")
	fmt.Println("- 浏览历史记录")
	fmt.Println("- 审计日志")
	fmt.Println("- 操作回溯分析")
}

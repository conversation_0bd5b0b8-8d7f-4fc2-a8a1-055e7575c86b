package main

import (
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"
)

var redisClient *redis.Client

func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis Set 类型详解 ===\n")
	fmt.Println("Set 是 Redis 中的无序、唯一字符串集合，支持集合运算操作。")

	// 清理环境
	redisClient.Del(ctx, "tags:user123", "visitors:today", "blacklist:ip", "friends:alice", "friends:bob", "online:users")

	// 1. 标签系统场景
	fmt.Println("\n1. 标签系统场景:")
	tagSystemExample(ctx)

	// 2. 唯一访问者统计场景
	fmt.Println("\n2. 唯一访问者统计场景:")
	uniqueVisitorsExample(ctx)

	// 3. 黑名单/白名单场景
	fmt.Println("\n3. 黑名单/白名单场景:")
	blacklistExample(ctx)

	// 4. 社交关系场景
	fmt.Println("\n4. 社交关系场景:")
	socialNetworkExample(ctx)

	// 5. 在线用户管理场景
	fmt.Println("\n5. 在线用户管理场景:")
	onlineUsersExample(ctx)
}

// 1. 标签系统场景
func tagSystemExample(ctx context.Context) {
	tagKey := "tags:user123"

	// 用户添加标签
	tags := []string{"技术", "编程", "Go语言", "Redis", "数据库", "后端开发"}

	fmt.Println("✓ 用户添加标签:")
	for _, tag := range tags {
		// 使用 SAdd 添加标签到集合
		added, err := redisClient.SAdd(ctx, tagKey, tag).Result()
		if err != nil {
			fmt.Printf("添加标签失败: %v\n", err)
			continue
		}
		if added > 0 {
			fmt.Printf("  - 添加标签: %s\n", tag)
		} else {
			fmt.Printf("  - 标签已存在: %s\n", tag)
		}
	}

	// 尝试添加重复标签
	fmt.Println("\n✓ 尝试添加重复标签:")
	duplicateTag := "Go语言"
	added, err := redisClient.SAdd(ctx, tagKey, duplicateTag).Result()
	if err != nil {
		fmt.Printf("添加标签失败: %v\n", err)
	} else if added == 0 {
		fmt.Printf("  - 标签 '%s' 已存在，未重复添加\n", duplicateTag)
	}

	// 获取所有标签
	fmt.Println("\n✓ 用户的所有标签:")
	allTags, err := redisClient.SMembers(ctx, tagKey).Result()
	if err != nil {
		fmt.Printf("获取标签失败: %v\n", err)
	} else {
		for i, tag := range allTags {
			fmt.Printf("  %d. %s\n", i+1, tag)
		}
	}

	// 检查特定标签是否存在
	checkTags := []string{"Redis", "Python", "前端"}
	fmt.Println("\n✓ 检查标签是否存在:")
	for _, tag := range checkTags {
		exists, err := redisClient.SIsMember(ctx, tagKey, tag).Result()
		if err != nil {
			fmt.Printf("检查标签失败: %v\n", err)
			continue
		}
		fmt.Printf("  - %s: %t\n", tag, exists)
	}

	// 获取标签数量
	count, err := redisClient.SCard(ctx, tagKey).Result()
	if err != nil {
		fmt.Printf("获取标签数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 标签总数: %d\n", count)
	}

	// 删除某个标签
	removedTag := "数据库"
	removed, err := redisClient.SRem(ctx, tagKey, removedTag).Result()
	if err != nil {
		fmt.Printf("删除标签失败: %v\n", err)
	} else {
		fmt.Printf("✓ 删除标签 '%s'，删除了 %d 个\n", removedTag, removed)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 用户兴趣标签")
	fmt.Println("- 商品分类标签")
	fmt.Println("- 内容标签系统")
	fmt.Println("- 技能标签管理")
}

// 2. 唯一访问者统计场景
func uniqueVisitorsExample(ctx context.Context) {
	visitorsKey := "visitors:today"

	// 模拟用户访问
	visitors := []string{
		"*************", "*************", "*************",
		"*************", // 重复访问
		"*************", "*************",
		"*************", // 重复访问
		"*************",
	}

	fmt.Println("✓ 记录用户访问:")
	for i, ip := range visitors {
		// 使用 SAdd 记录访问者IP
		added, err := redisClient.SAdd(ctx, visitorsKey, ip).Result()
		if err != nil {
			fmt.Printf("记录访问失败: %v\n", err)
			continue
		}

		if added > 0 {
			fmt.Printf("  访问 #%d: %s (新访问者)\n", i+1, ip)
		} else {
			fmt.Printf("  访问 #%d: %s (重复访问)\n", i+1, ip)
		}
	}

	// 获取今日唯一访问者数量
	uniqueCount, err := redisClient.SCard(ctx, visitorsKey).Result()
	if err != nil {
		fmt.Printf("获取访问者数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 今日唯一访问者数量: %d\n", uniqueCount)
	}

	// 获取所有唯一访问者
	fmt.Println("\n✓ 今日所有唯一访问者:")
	allVisitors, err := redisClient.SMembers(ctx, visitorsKey).Result()
	if err != nil {
		fmt.Printf("获取访问者列表失败: %v\n", err)
	} else {
		for i, visitor := range allVisitors {
			fmt.Printf("  %d. %s\n", i+1, visitor)
		}
	}

	// 检查特定IP是否访问过
	checkIP := "*************"
	visited, err := redisClient.SIsMember(ctx, visitorsKey, checkIP).Result()
	if err != nil {
		fmt.Printf("检查访问失败: %v\n", err)
	} else {
		fmt.Printf("✓ IP %s 今日是否访问过: %t\n", checkIP, visited)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 网站唯一访问者统计")
	fmt.Println("- 日活跃用户统计")
	fmt.Println("- IP访问记录")
	fmt.Println("- 去重计数器")
}

// 3. 黑名单/白名单场景
func blacklistExample(ctx context.Context) {
	blacklistKey := "blacklist:ip"

	// 添加黑名单IP
	blacklistIPs := []string{
		"*************", "*********", "************",
		"*************", "*********",
	}

	fmt.Println("✓ 添加IP到黑名单:")
	for _, ip := range blacklistIPs {
		err := redisClient.SAdd(ctx, blacklistKey, ip).Err()
		if err != nil {
			fmt.Printf("添加黑名单失败: %v\n", err)
			continue
		}
		fmt.Printf("  - %s\n", ip)
	}

	// 检查IP是否在黑名单中
	checkIPs := []string{
		"*************", "************", "*********", "*******",
	}

	fmt.Println("\n✓ 检查IP是否在黑名单中:")
	for _, ip := range checkIPs {
		isBlocked, err := redisClient.SIsMember(ctx, blacklistKey, ip).Result()
		if err != nil {
			fmt.Printf("检查黑名单失败: %v\n", err)
			continue
		}

		status := "允许"
		if isBlocked {
			status = "阻止"
		}
		fmt.Printf("  - %s: %s\n", ip, status)
	}

	// 获取黑名单总数
	blacklistCount, err := redisClient.SCard(ctx, blacklistKey).Result()
	if err != nil {
		fmt.Printf("获取黑名单数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 黑名单IP总数: %d\n", blacklistCount)
	}

	// 从黑名单中移除IP
	removeIP := "*************"
	removed, err := redisClient.SRem(ctx, blacklistKey, removeIP).Result()
	if err != nil {
		fmt.Printf("移除黑名单失败: %v\n", err)
	} else {
		fmt.Printf("✓ 从黑名单中移除IP %s，移除了 %d 个\n", removeIP, removed)
	}

	// 获取所有黑名单IP
	fmt.Println("\n✓ 当前黑名单:")
	allBlacklistIPs, err := redisClient.SMembers(ctx, blacklistKey).Result()
	if err != nil {
		fmt.Printf("获取黑名单失败: %v\n", err)
	} else {
		for i, ip := range allBlacklistIPs {
			fmt.Printf("  %d. %s\n", i+1, ip)
		}
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- IP黑名单管理")
	fmt.Println("- 用户黑名单")
	fmt.Println("- 权限白名单")
	fmt.Println("- 垃圾邮件过滤")
}

// 4. 社交关系场景
func socialNetworkExample(ctx context.Context) {
	aliceFriendsKey := "friends:alice"
	bobFriendsKey := "friends:bob"

	// Alice的朋友
	aliceFriends := []string{"charlie", "david", "eve", "frank", "grace"}
	// Bob的朋友
	bobFriends := []string{"david", "eve", "henry", "ivan", "jack"}

	// 添加Alice的朋友
	fmt.Println("✓ Alice的朋友列表:")
	for _, friend := range aliceFriends {
		err := redisClient.SAdd(ctx, aliceFriendsKey, friend).Err()
		if err != nil {
			fmt.Printf("添加朋友失败: %v\n", err)
			continue
		}
		fmt.Printf("  - %s\n", friend)
	}

	// 添加Bob的朋友
	fmt.Println("\n✓ Bob的朋友列表:")
	for _, friend := range bobFriends {
		err := redisClient.SAdd(ctx, bobFriendsKey, friend).Err()
		if err != nil {
			fmt.Printf("添加朋友失败: %v\n", err)
			continue
		}
		fmt.Printf("  - %s\n", friend)
	}

	// 查找共同朋友（交集）
	fmt.Println("\n✓ Alice和Bob的共同朋友:")
	commonFriends, err := redisClient.SInter(ctx, aliceFriendsKey, bobFriendsKey).Result()
	if err != nil {
		fmt.Printf("查找共同朋友失败: %v\n", err)
	} else {
		for i, friend := range commonFriends {
			fmt.Printf("  %d. %s\n", i+1, friend)
		}
		fmt.Printf("✓ 共同朋友数量: %d\n", len(commonFriends))
	}

	// 查找Alice独有的朋友（差集）
	fmt.Println("\n✓ Alice独有的朋友:")
	aliceOnlyFriends, err := redisClient.SDiff(ctx, aliceFriendsKey, bobFriendsKey).Result()
	if err != nil {
		fmt.Printf("查找Alice独有朋友失败: %v\n", err)
	} else {
		for i, friend := range aliceOnlyFriends {
			fmt.Printf("  %d. %s\n", i+1, friend)
		}
	}

	// 查找所有朋友（并集）
	fmt.Println("\n✓ Alice和Bob的所有朋友:")
	allFriends, err := redisClient.SUnion(ctx, aliceFriendsKey, bobFriendsKey).Result()
	if err != nil {
		fmt.Printf("查找所有朋友失败: %v\n", err)
	} else {
		for i, friend := range allFriends {
			fmt.Printf("  %d. %s\n", i+1, friend)
		}
		fmt.Printf("✓ 总朋友数量: %d\n", len(allFriends))
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 社交网络好友关系")
	fmt.Println("- 共同兴趣查找")
	fmt.Println("- 用户群组管理")
	fmt.Println("- 推荐系统基础数据")
}

// 5. 在线用户管理场景
func onlineUsersExample(ctx context.Context) {
	onlineUsersKey := "online:users"

	// 模拟用户上线
	fmt.Println("✓ 用户上线:")
	onlineUsers := []string{"user1", "user2", "user3", "user4", "user5"}
	for _, user := range onlineUsers {
		err := redisClient.SAdd(ctx, onlineUsersKey, user).Err()
		if err != nil {
			fmt.Printf("用户上线失败: %v\n", err)
			continue
		}
		fmt.Printf("  - %s 上线\n", user)
	}

	// 获取在线用户数量
	onlineCount, err := redisClient.SCard(ctx, onlineUsersKey).Result()
	if err != nil {
		fmt.Printf("获取在线用户数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 当前在线用户数量: %d\n", onlineCount)
	}

	// 检查特定用户是否在线
	checkUsers := []string{"user1", "user6", "user3"}
	fmt.Println("\n✓ 检查用户在线状态:")
	for _, user := range checkUsers {
		isOnline, err := redisClient.SIsMember(ctx, onlineUsersKey, user).Result()
		if err != nil {
			fmt.Printf("检查用户状态失败: %v\n", err)
			continue
		}

		status := "离线"
		if isOnline {
			status = "在线"
		}
		fmt.Printf("  - %s: %s\n", user, status)
	}

	// 随机获取一些在线用户（用于推荐或匹配）
	fmt.Println("\n✓ 随机获取2个在线用户:")
	randomUsers, err := redisClient.SRandMemberN(ctx, onlineUsersKey, 2).Result()
	if err != nil {
		fmt.Printf("获取随机用户失败: %v\n", err)
	} else {
		for i, user := range randomUsers {
			fmt.Printf("  %d. %s\n", i+1, user)
		}
	}

	// 模拟用户下线
	offlineUsers := []string{"user2", "user4"}
	fmt.Println("\n✓ 用户下线:")
	for _, user := range offlineUsers {
		removed, err := redisClient.SRem(ctx, onlineUsersKey, user).Result()
		if err != nil {
			fmt.Printf("用户下线失败: %v\n", err)
			continue
		}
		if removed > 0 {
			fmt.Printf("  - %s 下线\n", user)
		}
	}

	// 获取当前在线用户列表
	fmt.Println("\n✓ 当前在线用户:")
	currentOnlineUsers, err := redisClient.SMembers(ctx, onlineUsersKey).Result()
	if err != nil {
		fmt.Printf("获取在线用户失败: %v\n", err)
	} else {
		for i, user := range currentOnlineUsers {
			fmt.Printf("  %d. %s\n", i+1, user)
		}
	}

	// 获取更新后的在线用户数量
	finalCount, err := redisClient.SCard(ctx, onlineUsersKey).Result()
	if err != nil {
		fmt.Printf("获取最终在线用户数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 最终在线用户数量: %d\n", finalCount)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 在线用户状态管理")
	fmt.Println("- 实时用户匹配")
	fmt.Println("- 聊天室用户管理")
	fmt.Println("- 游戏在线玩家统计")
}

package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

var redisClient *redis.Client

func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis String 类型详解 ===\n")
	fmt.Println("String 是 Redis 最基本的数据类型，可以存储文本、数字、二进制数据等。")

	// 清理环境
	redisClient.Del(ctx, "user:token", "page:views", "product:stock", "distributed:lock", "cache:data")

	// 1. 缓存场景
	fmt.Println("\n1. 缓存场景示例:")
	cacheExample(ctx)

	// 2. 计数器场景
	fmt.Println("\n2. 计数器场景示例:")
	counterExample(ctx)

	// 3. 分布式锁场景
	fmt.Println("\n3. 分布式锁场景示例:")
	distributedLockExample(ctx)

	// 4. 限流场景
	fmt.Println("\n4. 限流场景示例:")
	rateLimitExample(ctx)

	// 5. 会话管理场景
	fmt.Println("\n5. 会话管理场景示例:")
	sessionManagementExample(ctx)
}

// 1. 缓存场景
func cacheExample(ctx context.Context) {
	// 模拟从数据库获取的数据
	data := `{"id":123,"name":"iPhone 13","price":5999,"description":"最新款手机"}`

	// 存储数据到缓存，设置过期时间为 1 小时
	err := redisClient.Set(ctx, "cache:data", data, 1*time.Hour).Err()
	if err != nil {
		fmt.Printf("缓存设置失败: %v\n", err)
		return
	}
	fmt.Println("✓ 数据已缓存，过期时间为 1 小时")

	// 模拟从缓存读取数据
	cachedData, err := redisClient.Get(ctx, "cache:data").Result()
	if err != nil {
		fmt.Printf("缓存读取失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 从缓存读取数据: %s\n", cachedData)

	// 检查剩余过期时间
	ttl, _ := redisClient.TTL(ctx, "cache:data").Result()
	fmt.Printf("✓ 缓存剩余时间: %v\n", ttl)

	fmt.Println("\n应用场景：")
	fmt.Println("- 网页内容缓存")
	fmt.Println("- API 响应缓存")
	fmt.Println("- 数据库查询结果缓存")
	fmt.Println("- 用户信息临时存储")
}

// 2. 计数器场景
func counterExample(ctx context.Context) {
	// 初始化计数器
	redisClient.Set(ctx, "page:views", "0", 0)
	fmt.Println("✓ 初始化页面访问计数器为 0")

	// 模拟多次页面访问，增加计数
	for i := 1; i <= 5; i++ {
		count, err := redisClient.Incr(ctx, "page:views").Result()
		if err != nil {
			fmt.Printf("计数器增加失败: %v\n", err)
			continue
		}
		fmt.Printf("✓ 第 %d 次访问，当前计数: %d\n", i, count)
	}

	// 获取最终计数
	finalCount, _ := redisClient.Get(ctx, "page:views").Int64()
	fmt.Printf("✓ 最终页面访问次数: %d\n", finalCount)

	fmt.Println("\n应用场景：")
	fmt.Println("- 网站访问统计")
	fmt.Println("- 点赞/投票计数")
	fmt.Println("- 库存计数")
	fmt.Println("- 限制 API 调用次数")
}

// 3. 分布式锁场景
func distributedLockExample(ctx context.Context) {
	// 尝试获取锁（使用 SetNX 命令）
	lockKey := "distributed:lock"
	lockValue := "process-id-12345" // 通常使用唯一标识符，如进程ID
	lockAcquired, err := redisClient.SetNX(ctx, lockKey, lockValue, 10*time.Second).Result()

	if err != nil {
		fmt.Printf("获取锁失败: %v\n", err)
		return
	}

	if lockAcquired {
		fmt.Println("✓ 成功获取锁，可以执行临界区操作")

		// 模拟执行一些操作
		fmt.Println("✓ 执行受保护的操作中...")
		time.Sleep(2 * time.Second)

		// 操作完成后，检查并释放锁（确保只释放自己的锁）
		currentValue, _ := redisClient.Get(ctx, lockKey).Result()
		if currentValue == lockValue {
			redisClient.Del(ctx, lockKey)
			fmt.Println("✓ 操作完成，锁已释放")
		}
	} else {
		fmt.Println("✓ 无法获取锁，其他进程正在执行操作")
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 防止并发操作同一资源")
	fmt.Println("- 确保分布式系统中的操作原子性")
	fmt.Println("- 防止重复处理")
	fmt.Println("- 定时任务的单实例执行")
}

// 4. 限流场景
func rateLimitExample(ctx context.Context) {
	userID := "user123"
	apiEndpoint := "api/search"
	rateLimitKey := fmt.Sprintf("ratelimit:%s:%s", userID, apiEndpoint)

	// 设置限流规则：每分钟最多 5 次请求
	maxRequests := 5
	windowSeconds := 60

	// 检查当前请求次数
	currentCount, err := redisClient.Get(ctx, rateLimitKey).Int64()
	if err != nil && err != redis.Nil {
		fmt.Printf("检查限流失败: %v\n", err)
		return
	}

	if err == redis.Nil {
		// 键不存在，这是第一次请求
		err = redisClient.Set(ctx, rateLimitKey, 1, time.Duration(windowSeconds)*time.Second).Err()
		if err != nil {
			fmt.Printf("设置限流计数失败: %v\n", err)
			return
		}
		fmt.Println("✓ 第 1 次请求，已允许")
	} else if currentCount < int64(maxRequests) {
		// 未超过限制，增加计数
		newCount, _ := redisClient.Incr(ctx, rateLimitKey).Result()
		fmt.Printf("✓ 第 %d 次请求，已允许\n", newCount)
	} else {
		// 已超过限制
		fmt.Printf("✓ 请求被限流，已达到限制 (%d/%d)\n", currentCount, maxRequests)

		// 获取剩余限流时间
		ttl, _ := redisClient.TTL(ctx, rateLimitKey).Result()
		fmt.Printf("✓ 请等待 %v 后重试\n", ttl)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- API 请求限流")
	fmt.Println("- 防止暴力破解")
	fmt.Println("- 控制资源使用")
	fmt.Println("- 保护系统免受过载")
}

// 5. 会话管理场景
func sessionManagementExample(ctx context.Context) {
	// 生成用户令牌
	userID := "user456"
	token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." // 实际中这是JWT令牌

	// 存储令牌，设置过期时间（如30分钟）
	tokenKey := fmt.Sprintf("user:token:%s", userID)
	err := redisClient.Set(ctx, tokenKey, token, 30*time.Minute).Err()
	if err != nil {
		fmt.Printf("存储令牌失败: %v\n", err)
		return
	}
	fmt.Println("✓ 用户登录成功，令牌已存储")

	// 模拟验证令牌
	storedToken, err := redisClient.Get(ctx, tokenKey).Result()
	if err != nil {
		if err == redis.Nil {
			fmt.Println("✓ 令牌不存在，用户未登录或会话已过期")
		} else {
			fmt.Printf("获取令牌失败: %v\n", err)
		}
		return
	}

	if storedToken == token {
		fmt.Println("✓ 令牌验证成功，用户已登录")

		// 刷新令牌过期时间（延长会话）
		redisClient.Expire(ctx, tokenKey, 30*time.Minute)
		fmt.Println("✓ 会话已延长30分钟")
	} else {
		fmt.Println("✓ 令牌验证失败，可能是伪造的")
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 用户会话管理")
	fmt.Println("- 身份验证令牌存储")
	fmt.Println("- 单点登录实现")
	fmt.Println("- 会话状态跟踪")
}

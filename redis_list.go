package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// 全局 Redis 客户端变量
var redisClient *redis.Client

// 初始化 Redis 客户端连接
func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // Redis 服务器地址
		Password: "",               // Redis 密码（无密码则为空字符串）
		DB:       0,                // 使用的数据库编号，默认0
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis List 类型详解 ===\n")
	fmt.Println("List 是 Redis 中的双向链表结构，支持在两端进行高效的插入和删除操作。")
	fmt.Println("每个 List 最多可以包含 2^32-1 个元素，适合实现队列、栈、时间线等数据结构。")
	fmt.Println("List 保持元素的插入顺序，支持按索引访问和范围操作。")

	// 清理环境
	redisClient.Del(ctx, "queue:tasks", "timeline:user123", "logs:system", "stack:operations", "notifications:user456")

	// 1. 消息队列场景 - 最经典的List应用
	fmt.Println("\n1. 消息队列场景:")
	fmt.Println("应用场景：任务队列、消息队列、异步处理、生产者消费者模式等")
	messageQueueExample(ctx)

	// 2. 时间线场景 - 按时间顺序存储
	fmt.Println("\n2. 时间线场景:")
	fmt.Println("应用场景：社交媒体动态、新闻时间线、操作历史记录等")
	timelineExample(ctx)

	// 3. 日志系统场景 - 日志收集和管理
	fmt.Println("\n3. 日志系统场景:")
	fmt.Println("应用场景：系统日志、访问日志、错误日志、审计日志等")
	logSystemExample(ctx)

	// 4. 栈操作场景 - LIFO数据结构
	fmt.Println("\n4. 栈操作场景:")
	fmt.Println("应用场景：撤销操作、浏览器历史、函数调用栈模拟等")
	stackOperationExample(ctx)

	// 5. 通知系统场景 - 消息推送管理
	fmt.Println("\n5. 通知系统场景:")
	fmt.Println("应用场景：用户通知、系统消息、推送消息管理等")
	notificationSystemExample(ctx)

	// 6. 基本操作演示
	fmt.Println("\n6. 基本操作演示:")
	basicListOperationsExample(ctx)
}

// 1. 消息队列场景
func messageQueueExample(ctx context.Context) {
	queueKey := "queue:tasks"
	
	fmt.Println("📬 任务队列系统:")
	
	// 生产者：向队列添加任务（从右端推入）
	tasks := []string{
		"发送邮件给用户123",
		"处理订单456",
		"生成报表",
		"清理临时文件",
		"备份数据库",
	}
	
	fmt.Println("🏭 生产者添加任务:")
	for i, task := range tasks {
		queueLength, err := redisClient.RPush(ctx, queueKey, task).Result()
		if err != nil {
			fmt.Printf("❌ 添加任务失败: %v\n", err)
			continue
		}
		fmt.Printf("  任务 #%d: %s (队列长度: %d)\n", i+1, task, queueLength)
	}
	
	// 查看队列状态
	queueLength, err := redisClient.LLen(ctx, queueKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取队列长度失败: %v\n", err)
	} else {
		fmt.Printf("✓ 当前队列长度: %d\n", queueLength)
	}
	
	// 消费者：从队列取出任务（从左端弹出，FIFO）
	fmt.Println("\n🏭 消费者处理任务:")
	for i := 1; i <= 3; i++ {
		task, err := redisClient.LPop(ctx, queueKey).Result()
		if err != nil {
			if err == redis.Nil {
				fmt.Println("  队列为空，没有任务可处理")
				break
			}
			fmt.Printf("❌ 获取任务失败: %v\n", err)
			continue
		}
		
		fmt.Printf("  消费者 #%d 处理任务: %s\n", i, task)
		// 模拟任务处理时间
		time.Sleep(100 * time.Millisecond)
		fmt.Printf("  ✓ 任务完成: %s\n", task)
	}
	
	// 查看剩余任务
	remainingTasks, err := redisClient.LRange(ctx, queueKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("❌ 获取剩余任务失败: %v\n", err)
	} else {
		fmt.Printf("✓ 剩余任务数: %d\n", len(remainingTasks))
		for i, task := range remainingTasks {
			fmt.Printf("  待处理 #%d: %s\n", i+1, task)
		}
	}
	
	// 阻塞式消费（模拟）
	fmt.Println("\n⏳ 阻塞式消费演示:")
	fmt.Println("  消费者等待新任务...")
	
	// 在另一个goroutine中添加任务
	go func() {
		time.Sleep(1 * time.Second)
		redisClient.RPush(ctx, queueKey, "紧急任务：处理用户投诉")
		fmt.Println("  ✓ 生产者添加了紧急任务")
	}()
	
	// 阻塞等待任务（最多等待2秒）
	task, err := redisClient.BLPop(ctx, 2*time.Second, queueKey).Result()
	if err != nil {
		fmt.Printf("❌ 阻塞获取任务失败: %v\n", err)
	} else {
		fmt.Printf("  ✓ 获取到任务: %s\n", task[1]) // task[0]是key，task[1]是value
	}
	
	fmt.Println("优势：FIFO保证任务顺序，支持阻塞操作，高并发安全")
}

// 2. 时间线场景
func timelineExample(ctx context.Context) {
	timelineKey := "timeline:user123"
	
	fmt.Println("📰 用户时间线:")
	
	// 用户发布动态（新动态添加到列表头部）
	posts := []string{
		fmt.Sprintf("[%s] 今天天气真好！", time.Now().Format("15:04:05")),
		fmt.Sprintf("[%s] 刚刚完成了一个项目", time.Now().Add(-5*time.Minute).Format("15:04:05")),
		fmt.Sprintf("[%s] 分享一篇技术文章", time.Now().Add(-10*time.Minute).Format("15:04:05")),
		fmt.Sprintf("[%s] 午餐时间到了", time.Now().Add(-2*time.Hour).Format("15:04:05")),
		fmt.Sprintf("[%s] 早上好，新的一天开始了", time.Now().Add(-8*time.Hour).Format("15:04:05")),
	}
	
	fmt.Println("📝 发布动态:")
	for i, post := range posts {
		timelineLength, err := redisClient.LPush(ctx, timelineKey, post).Result()
		if err != nil {
			fmt.Printf("❌ 发布动态失败: %v\n", err)
			continue
		}
		fmt.Printf("  动态 #%d: %s (时间线长度: %d)\n", i+1, post, timelineLength)
	}
	
	// 获取最新的3条动态
	fmt.Println("\n📱 最新动态 (前3条):")
	recentPosts, err := redisClient.LRange(ctx, timelineKey, 0, 2).Result()
	if err != nil {
		fmt.Printf("❌ 获取最新动态失败: %v\n", err)
	} else {
		for i, post := range recentPosts {
			fmt.Printf("  %d. %s\n", i+1, post)
		}
	}
	
	// 获取指定范围的动态（第2-4条）
	fmt.Println("\n📜 历史动态 (第2-4条):")
	historyPosts, err := redisClient.LRange(ctx, timelineKey, 1, 3).Result()
	if err != nil {
		fmt.Printf("❌ 获取历史动态失败: %v\n", err)
	} else {
		for i, post := range historyPosts {
			fmt.Printf("  %d. %s\n", i+2, post)
		}
	}
	
	// 限制时间线长度（只保留最新的10条）
	err = redisClient.LTrim(ctx, timelineKey, 0, 9).Err()
	if err != nil {
		fmt.Printf("❌ 修剪时间线失败: %v\n", err)
	} else {
		fmt.Println("✓ 时间线已修剪，只保留最新的10条动态")
	}
	
	// 在指定位置插入动态（比如置顶）
	err = redisClient.LInsert(ctx, timelineKey, "BEFORE", recentPosts[0], "📌 [置顶] 重要公告：系统维护通知").Err()
	if err != nil {
		fmt.Printf("❌ 插入置顶动态失败: %v\n", err)
	} else {
		fmt.Println("✓ 已插入置顶动态")
	}
	
	// 查看更新后的时间线
	fmt.Println("\n📋 完整时间线:")
	allPosts, err := redisClient.LRange(ctx, timelineKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("❌ 获取完整时间线失败: %v\n", err)
	} else {
		for i, post := range allPosts {
			fmt.Printf("  %d. %s\n", i+1, post)
		}
	}
	
	fmt.Println("优势：保持时间顺序，支持分页查询，高效的头部插入")
}

// 3. 日志系统场景
func logSystemExample(ctx context.Context) {
	logKey := "logs:system"
	
	fmt.Println("📋 系统日志管理:")
	
	// 模拟系统产生日志
	logEntries := []string{
		fmt.Sprintf("[ERROR] %s - 数据库连接失败", time.Now().Format("2006-01-02 15:04:05")),
		fmt.Sprintf("[WARN]  %s - 内存使用率超过80%%", time.Now().Add(-1*time.Minute).Format("2006-01-02 15:04:05")),
		fmt.Sprintf("[INFO]  %s - 用户登录成功 UserID:1001", time.Now().Add(-2*time.Minute).Format("2006-01-02 15:04:05")),
		fmt.Sprintf("[DEBUG] %s - 缓存命中率: 95%%", time.Now().Add(-3*time.Minute).Format("2006-01-02 15:04:05")),
		fmt.Sprintf("[INFO]  %s - 系统启动完成", time.Now().Add(-5*time.Minute).Format("2006-01-02 15:04:05")),
	}
	
	fmt.Println("📝 收集日志:")
	for i, logEntry := range logEntries {
		logCount, err := redisClient.RPush(ctx, logKey, logEntry).Result()
		if err != nil {
			fmt.Printf("❌ 添加日志失败: %v\n", err)
			continue
		}
		fmt.Printf("  日志 #%d: %s (总日志数: %d)\n", i+1, logEntry, logCount)
	}
	
	// 获取最新的日志
	fmt.Println("\n🔍 最新日志 (最后3条):")
	recentLogs, err := redisClient.LRange(ctx, logKey, -3, -1).Result()
	if err != nil {
		fmt.Printf("❌ 获取最新日志失败: %v\n", err)
	} else {
		for i, log := range recentLogs {
			fmt.Printf("  %d. %s\n", i+1, log)
		}
	}
	
	// 获取错误日志（简单过滤）
	fmt.Println("\n🚨 错误日志筛选:")
	allLogs, err := redisClient.LRange(ctx, logKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("❌ 获取所有日志失败: %v\n", err)
	} else {
		errorCount := 0
		for _, log := range allLogs {
			if len(log) > 7 && log[1:6] == "ERROR" {
				errorCount++
				fmt.Printf("  错误 #%d: %s\n", errorCount, log)
			}
		}
		if errorCount == 0 {
			fmt.Println("  ✓ 没有发现错误日志")
		}
	}
	
	// 日志轮转（保持最新的1000条日志）
	maxLogs := int64(1000)
	currentLogCount, err := redisClient.LLen(ctx, logKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取日志数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 当前日志数量: %d\n", currentLogCount)
		
		if currentLogCount > maxLogs {
			err = redisClient.LTrim(ctx, logKey, -maxLogs, -1).Err()
			if err != nil {
				fmt.Printf("❌ 日志轮转失败: %v\n", err)
			} else {
				fmt.Printf("✓ 日志已轮转，保留最新的 %d 条\n", maxLogs)
			}
		} else {
			fmt.Printf("✓ 日志数量未超限，无需轮转\n")
		}
	}
	
	// 按索引获取特定日志
	specificLog, err := redisClient.LIndex(ctx, logKey, 0).Result()
	if err != nil {
		fmt.Printf("❌ 获取特定日志失败: %v\n", err)
	} else {
		fmt.Printf("✓ 第一条日志: %s\n", specificLog)
	}
	
	fmt.Println("优势：按时间顺序存储，支持日志轮转，高效的尾部追加")
}

// 4. 栈操作场景
func stackOperationExample(ctx context.Context) {
	stackKey := "stack:operations"
	
	fmt.Println("📚 操作栈演示 (LIFO - 后进先出):")
	
	// 模拟用户操作历史
	operations := []string{
		"创建文档",
		"添加标题",
		"插入图片",
		"修改段落1",
		"添加表格",
	}
	
	fmt.Println("📝 执行操作 (压栈):")
	for i, operation := range operations {
		stackSize, err := redisClient.LPush(ctx, stackKey, operation).Result()
		if err != nil {
			fmt.Printf("❌ 压栈失败: %v\n", err)
			continue
		}
		fmt.Printf("  操作 #%d: %s (栈大小: %d)\n", i+1, operation, stackSize)
	}
	
	// 查看栈顶元素（不弹出）
	topOperation, err := redisClient.LIndex(ctx, stackKey, 0).Result()
	if err != nil {
		fmt.Printf("❌ 查看栈顶失败: %v\n", err)
	} else {
		fmt.Printf("✓ 栈顶操作: %s\n", topOperation)
	}
	
	// 撤销操作（弹栈）
	fmt.Println("\n↩️ 撤销操作 (出栈):")
	for i := 1; i <= 3; i++ {
		operation, err := redisClient.LPop(ctx, stackKey).Result()
		if err != nil {
			if err == redis.Nil {
				fmt.Println("  栈为空，无法撤销")
				break
			}
			fmt.Printf("❌ 撤销操作失败: %v\n", err)
			continue
		}
		fmt.Printf("  撤销 #%d: %s\n", i, operation)
	}
	
	// 查看剩余操作历史
	fmt.Println("\n📜 剩余操作历史:")
	remainingOps, err := redisClient.LRange(ctx, stackKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("❌ 获取操作历史失败: %v\n", err)
	} else {
		if len(remainingOps) == 0 {
			fmt.Println("  操作历史为空")
		} else {
			for i, op := range remainingOps {
				fmt.Printf("  %d. %s\n", i+1, op)
			}
		}
	}
	
	// 重做操作（模拟）
	fmt.Println("\n↪️ 重做操作:")
	redoOperations := []string{"添加表格", "修改段落1"}
	for i, operation := range redoOperations {
		stackSize, err := redisClient.LPush(ctx, stackKey, operation).Result()
		if err != nil {
			fmt.Printf("❌ 重做操作失败: %v\n", err)
			continue
		}
		fmt.Printf("  重做 #%d: %s (栈大小: %d)\n", i+1, operation, stackSize)
	}
	
	fmt.Println("优势：LIFO特性完美支持撤销/重做，操作历史管理简单高效")
}

// 5. 通知系统场景
func notificationSystemExample(ctx context.Context) {
	notificationKey := "notifications:user456"
	
	fmt.Println("🔔 用户通知系统:")
	
	// 系统发送通知
	notifications := []string{
		fmt.Sprintf("[系统] %s - 您的订单已发货", time.Now().Format("15:04")),
		fmt.Sprintf("[好友] %s - 张三给您发送了好友请求", time.Now().Add(-10*time.Minute).Format("15:04")),
		fmt.Sprintf("[活动] %s - 双11活动即将开始", time.Now().Add(-30*time.Minute).Format("15:04")),
		fmt.Sprintf("[安全] %s - 检测到异地登录", time.Now().Add(-1*time.Hour).Format("15:04")),
	}
	
	fmt.Println("📨 发送通知:")
	for i, notification := range notifications {
		notificationCount, err := redisClient.LPush(ctx, notificationKey, notification).Result()
		if err != nil {
			fmt.Printf("❌ 发送通知失败: %v\n", err)
			continue
		}
		fmt.Printf("  通知 #%d: %s (未读: %d)\n", i+1, notification, notificationCount)
	}
	
	// 获取未读通知数量
	unreadCount, err := redisClient.LLen(ctx, notificationKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取未读数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 用户有 %d 条未读通知\n", unreadCount)
	}
	
	// 用户查看最新通知
	fmt.Println("\n👀 查看最新通知:")
	latestNotifications, err := redisClient.LRange(ctx, notificationKey, 0, 2).Result()
	if err != nil {
		fmt.Printf("❌ 获取最新通知失败: %v\n", err)
	} else {
		for i, notification := range latestNotifications {
			fmt.Printf("  %d. %s\n", i+1, notification)
		}
	}
	
	// 标记通知为已读（移除）
	fmt.Println("\n✅ 标记通知已读:")
	for i := 1; i <= 2; i++ {
		readNotification, err := redisClient.LPop(ctx, notificationKey).Result()
		if err != nil {
			if err == redis.Nil {
				fmt.Println("  没有更多通知")
				break
			}
			fmt.Printf("❌ 标记已读失败: %v\n", err)
			continue
		}
		fmt.Printf("  已读 #%d: %s\n", i, readNotification)
	}
	
	// 批量标记已读（保留最新的N条）
	keepCount := int64(1)
	err = redisClient.LTrim(ctx, notificationKey, 0, keepCount-1).Err()
	if err != nil {
		fmt.Printf("❌ 批量标记已读失败: %v\n", err)
	} else {
		fmt.Printf("✓ 批量标记已读，保留最新 %d 条\n", keepCount)
	}
	
	// 查看剩余通知
	fmt.Println("\n📋 剩余通知:")
	remainingNotifications, err := redisClient.LRange(ctx, notificationKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("❌ 获取剩余通知失败: %v\n", err)
	} else {
		if len(remainingNotifications) == 0 {
			fmt.Println("  所有通知已读")
		} else {
			for i, notification := range remainingNotifications {
				fmt.Printf("  %d. %s\n", i+1, notification)
			}
		}
	}
	
	fmt.Println("优势：按时间顺序管理通知，支持批量操作，高效的已读标记")
}

// 6. 基本操作演示
func basicListOperationsExample(ctx context.Context) {
	demoKey := "demo:list"
	
	fmt.Println("🔧 List基本操作演示:")
	
	// 清理
	redisClient.Del(ctx, demoKey)
	
	// LPUSH - 从左端推入
	redisClient.LPush(ctx, demoKey, "left1", "left2")
	fmt.Println("✓ LPUSH: 从左端推入 left1, left2")
	
	// RPUSH - 从右端推入
	redisClient.RPush(ctx, demoKey, "right1", "right2")
	fmt.Println("✓ RPUSH: 从右端推入 right1, right2")
	
	// LRANGE - 获取范围元素
	elements, _ := redisClient.LRange(ctx, demoKey, 0, -1).Result()
	fmt.Printf("✓ LRANGE 0 -1: %v\n", elements)
	
	// LLEN - 获取长度
	length, _ := redisClient.LLen(ctx, demoKey).Result()
	fmt.Printf("✓ LLEN: %d\n", length)
	
	// LINDEX - 按索引获取
	element, _ := redisClient.LIndex(ctx, demoKey, 0).Result()
	fmt.Printf("✓ LINDEX 0: %s\n", element)
	
	// LSET - 按索引设置
	redisClient.LSet(ctx, demoKey, 1, "modified")
	fmt.Println("✓ LSET: 修改索引1的元素为 'modified'")
	
	// LPOP - 从左端弹出
	leftPop, _ := redisClient.LPop(ctx, demoKey).Result()
	fmt.Printf("✓ LPOP: %s\n", leftPop)
	
	// RPOP - 从右端弹出
	rightPop, _ := redisClient.RPop(ctx, demoKey).Result()
	fmt.Printf("✓ RPOP: %s\n", rightPop)
	
	// LINSERT - 插入元素
	redisClient.LInsert(ctx, demoKey, "BEFORE", "modified", "inserted")
	fmt.Println("✓ LINSERT: 在 'modified' 前插入 'inserted'")
	
	// LREM - 移除元素
	removed, _ := redisClient.LRem(ctx, demoKey, 1, "inserted").Result()
	fmt.Printf("✓ LREM: 移除了 %d 个 'inserted'\n", removed)
	
	// 最终状态
	finalElements, _ := redisClient.LRange(ctx, demoKey, 0, -1).Result()
	fmt.Printf("✓ 最终状态: %v\n", finalElements)
	
	fmt.Println("✓ List基本操作演示完成")
}

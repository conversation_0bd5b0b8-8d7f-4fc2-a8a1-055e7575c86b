package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
)

var redisClient *redis.Client

func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis Hash 类型详解 ===\n")
	fmt.Println("Hash 是 Redis 中的字典结构，适合存储对象数据，每个 Hash 可以存储多个字段-值对。")

	// 清理环境
	redisClient.Del(ctx, "user:1001", "product:10086", "cart:user123", "config:app", "article:comments")

	// 1. 用户信息存储场景
	fmt.Println("\n1. 用户信息存储场景:")
	userProfileExample(ctx)

	// 2. 购物车场景
	fmt.Println("\n2. 购物车场景:")
	shoppingCartExample(ctx)

	// 3. 配置信息场景
	fmt.Println("\n3. 配置信息场景:")
	configurationExample(ctx)

	// 4. 实时计数器场景
	fmt.Println("\n4. 实时计数器场景:")
	realTimeCounterExample(ctx)

	// 5. 评论系统场景
	fmt.Println("\n5. 评论系统场景:")
	commentSystemExample(ctx)
}

// 1. 用户信息存储场景
func userProfileExample(ctx context.Context) {
	// 存储用户信息
	userFields := map[string]interface{}{
		"id":         "1001",
		"username":   "zhangsan",
		"email":      "<EMAIL>",
		"age":        "28",
		"city":       "北京",
		"created_at": time.Now().Format(time.RFC3339),
		"is_active":  "true",
	}

	// 使用 HSet 设置多个字段
	err := redisClient.HSet(ctx, "user:1001", userFields).Err()
	if err != nil {
		fmt.Printf("设置用户信息失败: %v\n", err)
		return
	}
	fmt.Println("✓ 用户信息已存储")

	// 获取单个字段
	username, err := redisClient.HGet(ctx, "user:1001", "username").Result()
	if err != nil {
		fmt.Printf("获取用户名失败: %v\n", err)
	} else {
		fmt.Printf("✓ 用户名: %s\n", username)
	}

	// 获取多个字段
	values, err := redisClient.HMGet(ctx, "user:1001", "email", "city", "age").Result()
	if err != nil {
		fmt.Printf("获取多个字段失败: %v\n", err)
	} else {
		fmt.Printf("✓ 邮箱: %s, 城市: %s, 年龄: %s\n", values[0], values[1], values[2])
	}

	// 获取所有字段
	allFields, err := redisClient.HGetAll(ctx, "user:1001").Result()
	if err != nil {
		fmt.Printf("获取所有字段失败: %v\n", err)
	} else {
		fmt.Println("✓ 用户完整信息:")
		for field, value := range allFields {
			fmt.Printf("  - %s: %s\n", field, value)
		}
	}

	// 更新单个字段
	err = redisClient.HSet(ctx, "user:1001", "age", "29").Err()
	if err != nil {
		fmt.Printf("更新年龄失败: %v\n", err)
	} else {
		fmt.Println("✓ 已更新用户年龄为 29")
	}

	// 检查字段是否存在
	exists, err := redisClient.HExists(ctx, "user:1001", "phone").Result()
	if err != nil {
		fmt.Printf("检查字段失败: %v\n", err)
	} else {
		fmt.Printf("✓ 手机号字段存在? %t\n", exists)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 用户资料存储")
	fmt.Println("- 会话信息管理")
	fmt.Println("- 缓存用户数据")
	fmt.Println("- 用户设置存储")
}

// 2. 购物车场景
func shoppingCartExample(ctx context.Context) {
	// 清空购物车
	redisClient.Del(ctx, "cart:user123")
	fmt.Println("✓ 购物车已清空")

	// 添加商品到购物车
	cartItems := map[string]interface{}{
		"product:101": "2", // 2个商品101
		"product:102": "1", // 1个商品102
		"product:103": "3", // 3个商品103
	}
	err := redisClient.HSet(ctx, "cart:user123", cartItems).Err()
	if err != nil {
		fmt.Printf("添加商品失败: %v\n", err)
		return
	}
	fmt.Println("✓ 已添加商品到购物车")

	// 获取购物车中的所有商品
	items, err := redisClient.HGetAll(ctx, "cart:user123").Result()
	if err != nil {
		fmt.Printf("获取购物车失败: %v\n", err)
	} else {
		fmt.Println("✓ 购物车内容:")
		for productID, quantity := range items {
			fmt.Printf("  - 商品ID: %s, 数量: %s\n", productID, quantity)
		}
	}

	// 增加商品数量
	newQuantity, err := redisClient.HIncrBy(ctx, "cart:user123", "product:101", 1).Result()
	if err != nil {
		fmt.Printf("增加商品数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ 商品101数量增加1，现在有 %d 个\n", newQuantity)
	}

	// 删除购物车中的商品
	deleted, err := redisClient.HDel(ctx, "cart:user123", "product:103").Result()
	if err != nil {
		fmt.Printf("删除商品失败: %v\n", err)
	} else {
		fmt.Printf("✓ 已从购物车中删除 %d 种商品\n", deleted)
	}

	// 获取购物车商品数量
	count, err := redisClient.HLen(ctx, "cart:user123").Result()
	if err != nil {
		fmt.Printf("获取商品种类失败: %v\n", err)
	} else {
		fmt.Printf("✓ 购物车中有 %d 种商品\n", count)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 电商购物车")
	fmt.Println("- 订单商品列表")
	fmt.Println("- 收藏夹")
	fmt.Println("- 批量操作清单")
}

// 3. 配置信息场景
func configurationExample(ctx context.Context) {
	// 存储应用配置
	configData := map[string]interface{}{
		"db_host":         "localhost",
		"db_port":         "3306",
		"db_name":         "myapp",
		"redis_host":      "localhost",
		"redis_port":      "6379",
		"max_connections": "100",
		"timeout_seconds": "30",
		"debug_mode":      "true",
		"log_level":       "info",
		"allowed_origins": "example.com,api.example.com",
	}

	err := redisClient.HSet(ctx, "config:app", configData).Err()
	if err != nil {
		fmt.Printf("存储配置失败: %v\n", err)
		return
	}
	fmt.Println("✓ 应用配置已存储")

	// 获取特定配置项
	dbHost, err := redisClient.HGet(ctx, "config:app", "db_host").Result()
	if err != nil {
		fmt.Printf("获取配置项失败: %v\n", err)
	} else {
		fmt.Printf("✓ 数据库主机: %s\n", dbHost)
	}

	// 更新配置
	err = redisClient.HSet(ctx, "config:app", "log_level", "debug").Err()
	if err != nil {
		fmt.Printf("更新配置失败: %v\n", err)
	} else {
		fmt.Println("✓ 已更新日志级别为 debug")
	}

	// 获取所有配置
	allConfig, err := redisClient.HGetAll(ctx, "config:app").Result()
	if err != nil {
		fmt.Printf("获取所有配置失败: %v\n", err)
	} else {
		fmt.Println("✓ 当前配置:")
		for key, value := range allConfig {
			fmt.Printf("  - %s: %s\n", key, value)
		}
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 应用程序配置")
	fmt.Println("- 系统参数存储")
	fmt.Println("- 功能开关管理")
	fmt.Println("- 环境变量存储")
}

// 4. 实时计数器场景
func realTimeCounterExample(ctx context.Context) {
	// 初始化计数器
	statsKey := "stats:website:today"
	redisClient.Del(ctx, statsKey)

	initialStats := map[string]interface{}{
		"page_views":    "0",
		"unique_users":  "0",
		"logins":        "0",
		"registrations": "0",
		"api_calls":     "0",
		"errors":        "0",
	}

	err := redisClient.HSet(ctx, statsKey, initialStats).Err()
	if err != nil {
		fmt.Printf("初始化计数器失败: %v\n", err)
		return
	}
	fmt.Println("✓ 网站统计计数器已初始化")

	// 模拟一些操作，增加计数
	operations := []struct {
		field string
		count int64
	}{
		{"page_views", 50},
		{"unique_users", 20},
		{"logins", 15},
		{"api_calls", 120},
		{"errors", 3},
	}

	for _, op := range operations {
		newValue, err := redisClient.HIncrBy(ctx, statsKey, op.field, op.count).Result()
		if err != nil {
			fmt.Printf("增加计数失败: %v\n", err)
		} else {
			fmt.Printf("✓ %s 增加 %d，现在是 %d\n", op.field, op.count, newValue)
		}
	}

	// 获取所有统计数据
	stats, err := redisClient.HGetAll(ctx, statsKey).Result()
	if err != nil {
		fmt.Printf("获取统计数据失败: %v\n", err)
	} else {
		fmt.Println("✓ 今日网站统计:")
		for metric, count := range stats {
			fmt.Printf("  - %s: %s\n", metric, count)
		}
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 网站访问统计")
	fmt.Println("- 实时监控指标")
	fmt.Println("- 业务数据分析")
	fmt.Println("- 错误日志聚合")
}

// 5. 评论系统场景
func commentSystemExample(ctx context.Context) {
	// 文章ID
	articleID := "article:12345"
	commentKey := articleID + ":comments"

	// 清空之前的评论
	redisClient.Del(ctx, commentKey)

	// 添加一些评论
	for i := 1; i <= 3; i++ {
		commentID := fmt.Sprintf("comment:%d", i)
		comment := map[string]interface{}{
			"user_id":    fmt.Sprintf("user:%d", 100+i),
			"username":   fmt.Sprintf("用户%d", i),
			"content":    fmt.Sprintf("这是第%d条评论内容", i),
			"created_at": time.Now().Add(time.Duration(-i) * time.Hour).Format(time.RFC3339),
			"likes":      strconv.Itoa(i * 5),
		}

		err := redisClient.HSet(ctx, commentKey+":"+commentID, comment).Err()
		if err != nil {
			fmt.Printf("添加评论失败: %v\n", err)
			continue
		}
		fmt.Printf("✓ 已添加评论 #%d\n", i)
	}

	// 获取特定评论
	commentID := "comment:2"
	comment, err := redisClient.HGetAll(ctx, commentKey+":"+commentID).Result()
	if err != nil {
		fmt.Printf("获取评论失败: %v\n", err)
	} else {
		fmt.Printf("✓ 评论 #2 详情:\n")
		for field, value := range comment {
			fmt.Printf("  - %s: %s\n", field, value)
		}
	}

	// 增加点赞数
	likes, err := redisClient.HIncrBy(ctx, commentKey+":"+commentID, "likes", 1).Result()
	if err != nil {
		fmt.Printf("点赞失败: %v\n", err)
	} else {
		fmt.Printf("✓ 评论 #2 点赞数增加到 %d\n", likes)
	}

	fmt.Println("\n应用场景：")
	fmt.Println("- 社交媒体评论系统")
	fmt.Println("- 产品评价系统")
	fmt.Println("- 博客评论功能")
	fmt.Println("- 问答平台回复")
}

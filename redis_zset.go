package main

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// 全局 Redis 客户端变量
var redisClient *redis.Client

// 初始化 Redis 客户端连接
func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // Redis 服务器地址
		Password: "",               // Redis 密码（无密码则为空字符串）
		DB:       0,                // 使用的数据库编号，默认0
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis ZSet (有序集合) 类型详解 ===\n")
	fmt.Println("ZSet 是 Redis 中的有序集合，每个元素都关联一个分数(score)。")
	fmt.Println("元素按分数排序，分数相同时按字典序排序，支持范围查询和排名操作。")
	fmt.Println("每个 ZSet 最多可以包含 2^32-1 个元素，适合实现排行榜、优先队列等。")

	// 清理环境
	redisClient.Del(ctx, "leaderboard:game", "trending:articles", "tasks:priority", 
		"students:scores", "products:sales")

	// 1. 游戏排行榜场景 - 最经典的ZSet应用
	fmt.Println("\n1. 游戏排行榜场景:")
	fmt.Println("应用场景：游戏积分榜、用户等级排名、竞赛排名等")
	gameLeaderboardExample(ctx)

	// 2. 热门内容排序场景 - 基于热度排序
	fmt.Println("\n2. 热门内容排序场景:")
	fmt.Println("应用场景：热门文章、热搜榜、商品销量排行等")
	trendingContentExample(ctx)

	// 3. 任务优先级队列场景 - 优先级管理
	fmt.Println("\n3. 任务优先级队列场景:")
	fmt.Println("应用场景：任务调度、消息优先级、作业队列等")
	priorityQueueExample(ctx)

	// 4. 成绩管理场景 - 分数统计分析
	fmt.Println("\n4. 成绩管理场景:")
	fmt.Println("应用场景：学生成绩、考试排名、绩效评估等")
	scoreManagementExample(ctx)

	// 5. 时间序列数据场景 - 基于时间戳排序
	fmt.Println("\n5. 时间序列数据场景:")
	fmt.Println("应用场景：日志按时间排序、事件时间线、数据归档等")
	timeSeriesExample(ctx)

	// 6. 基本操作演示
	fmt.Println("\n6. 基本操作演示:")
	basicZSetOperationsExample(ctx)
}

// 1. 游戏排行榜场景
func gameLeaderboardExample(ctx context.Context) {
	leaderboardKey := "leaderboard:game"
	
	fmt.Println("🏆 游戏排行榜系统:")
	
	// 玩家得分数据
	players := []redis.Z{
		{Score: 9500, Member: "Alice"},
		{Score: 8800, Member: "Bob"},
		{Score: 9200, Member: "Charlie"},
		{Score: 7600, Member: "David"},
		{Score: 8900, Member: "Eve"},
		{Score: 9100, Member: "Frank"},
		{Score: 8500, Member: "Grace"},
		{Score: 9800, Member: "Henry"},
	}
	
	fmt.Println("📝 初始化玩家分数:")
	for i, player := range players {
		err := redisClient.ZAdd(ctx, leaderboardKey, &player).Err()
		if err != nil {
			fmt.Printf("❌ 添加玩家分数失败: %v\n", err)
			continue
		}
		fmt.Printf("  %d. %s: %.0f分\n", i+1, player.Member, player.Score)
	}
	
	// 玩家完成新关卡，增加分数
	fmt.Println("\n🎮 玩家游戏进展:")
	
	// Alice完成困难关卡，获得500分
	newScore, err := redisClient.ZIncrBy(ctx, leaderboardKey, 500, "Alice").Result()
	if err != nil {
		fmt.Printf("❌ 更新Alice分数失败: %v\n", err)
	} else {
		fmt.Printf("✓ Alice完成困难关卡，新分数: %.0f\n", newScore)
	}
	
	// Bob完成普通关卡，获得200分
	newScore, err = redisClient.ZIncrBy(ctx, leaderboardKey, 200, "Bob").Result()
	if err != nil {
		fmt.Printf("❌ 更新Bob分数失败: %v\n", err)
	} else {
		fmt.Printf("✓ Bob完成普通关卡，新分数: %.0f\n", newScore)
	}
	
	// 获取排行榜前5名（按分数降序）
	fmt.Println("\n🏅 排行榜 TOP 5:")
	topPlayers, err := redisClient.ZRevRangeWithScores(ctx, leaderboardKey, 0, 4).Result()
	if err != nil {
		fmt.Printf("❌ 获取排行榜失败: %v\n", err)
	} else {
		for i, player := range topPlayers {
			fmt.Printf("  第%d名: %s - %.0f分\n", i+1, player.Member, player.Score)
		}
	}
	
	// 查询特定玩家的排名
	fmt.Println("\n🔍 玩家排名查询:")
	checkPlayers := []string{"Alice", "Charlie", "David"}
	for _, playerName := range checkPlayers {
		rank, err := redisClient.ZRevRank(ctx, leaderboardKey, playerName).Result()
		if err != nil {
			fmt.Printf("❌ 查询%s排名失败: %v\n", playerName, err)
			continue
		}
		
		score, err := redisClient.ZScore(ctx, leaderboardKey, playerName).Result()
		if err != nil {
			fmt.Printf("❌ 查询%s分数失败: %v\n", playerName, err)
			continue
		}
		
		fmt.Printf("  %s: 第%d名, %.0f分\n", playerName, rank+1, score)
	}
	
	// 获取分数范围内的玩家
	fmt.Println("\n📊 分数段统计 (8000-9500分):")
	midRangePlayers, err := redisClient.ZRangeByScoreWithScores(ctx, leaderboardKey, &redis.ZRangeBy{
		Min: "8000",
		Max: "9500",
	}).Result()
	if err != nil {
		fmt.Printf("❌ 获取分数段玩家失败: %v\n", err)
	} else {
		fmt.Printf("  该分数段共有 %d 名玩家:\n", len(midRangePlayers))
		for i, player := range midRangePlayers {
			fmt.Printf("    %d. %s: %.0f分\n", i+1, player.Member, player.Score)
		}
	}
	
	// 获取总玩家数
	totalPlayers, err := redisClient.ZCard(ctx, leaderboardKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取总玩家数失败: %v\n", err)
	} else {
		fmt.Printf("✓ 排行榜总玩家数: %d\n", totalPlayers)
	}
	
	fmt.Println("优势：自动排序，高效的排名查询，支持分数范围查询")
}

// 2. 热门内容排序场景
func trendingContentExample(ctx context.Context) {
	trendingKey := "trending:articles"
	
	fmt.Println("📈 热门文章排行:")
	
	// 文章热度数据（基于浏览量、点赞数等综合计算）
	articles := []redis.Z{
		{Score: 1250, Member: "Redis入门教程"},
		{Score: 980, Member: "Go语言并发编程"},
		{Score: 1100, Member: "Docker容器化实践"},
		{Score: 850, Member: "MySQL性能优化"},
		{Score: 1350, Member: "微服务架构设计"},
		{Score: 750, Member: "JavaScript异步编程"},
	}
	
	fmt.Println("📝 初始化文章热度:")
	for i, article := range articles {
		err := redisClient.ZAdd(ctx, trendingKey, &article).Err()
		if err != nil {
			fmt.Printf("❌ 添加文章失败: %v\n", err)
			continue
		}
		fmt.Printf("  %d. %s (热度: %.0f)\n", i+1, article.Member, article.Score)
	}
	
	// 模拟用户互动，更新文章热度
	fmt.Println("\n👍 用户互动更新:")
	
	// "Redis入门教程" 获得大量点赞和分享
	newHotness, err := redisClient.ZIncrBy(ctx, trendingKey, 300, "Redis入门教程").Result()
	if err != nil {
		fmt.Printf("❌ 更新Redis教程热度失败: %v\n", err)
	} else {
		fmt.Printf("✓ 'Redis入门教程' 热度上升，新热度: %.0f\n", newHotness)
	}
	
	// "Go语言并发编程" 也获得关注
	newHotness, err = redisClient.ZIncrBy(ctx, trendingKey, 200, "Go语言并发编程").Result()
	if err != nil {
		fmt.Printf("❌ 更新Go教程热度失败: %v\n", err)
	} else {
		fmt.Printf("✓ 'Go语言并发编程' 热度上升，新热度: %.0f\n", newHotness)
	}
	
	// 获取热门文章排行榜
	fmt.Println("\n🔥 热门文章 TOP 3:")
	hotArticles, err := redisClient.ZRevRangeWithScores(ctx, trendingKey, 0, 2).Result()
	if err != nil {
		fmt.Printf("❌ 获取热门文章失败: %v\n", err)
	} else {
		for i, article := range hotArticles {
			fmt.Printf("  🏆 第%d名: %s (热度: %.0f)\n", i+1, article.Member, article.Score)
		}
	}
	
	// 获取中等热度的文章
	fmt.Println("\n📊 中等热度文章 (热度800-1200):")
	mediumHotArticles, err := redisClient.ZRangeByScoreWithScores(ctx, trendingKey, &redis.ZRangeBy{
		Min: "800",
		Max: "1200",
	}).Result()
	if err != nil {
		fmt.Printf("❌ 获取中等热度文章失败: %v\n", err)
	} else {
		for i, article := range mediumHotArticles {
			fmt.Printf("  %d. %s (热度: %.0f)\n", i+1, article.Member, article.Score)
		}
	}
	
	// 移除热度过低的文章
	fmt.Println("\n🗑️ 清理低热度文章 (热度<800):")
	removedCount, err := redisClient.ZRemRangeByScore(ctx, trendingKey, "-inf", "799").Result()
	if err != nil {
		fmt.Printf("❌ 清理低热度文章失败: %v\n", err)
	} else {
		fmt.Printf("✓ 已移除 %d 篇低热度文章\n", removedCount)
	}
	
	// 查看最终排行
	fmt.Println("\n📋 最终热门排行:")
	finalRanking, err := redisClient.ZRevRangeWithScores(ctx, trendingKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("❌ 获取最终排行失败: %v\n", err)
	} else {
		for i, article := range finalRanking {
			fmt.Printf("  %d. %s (热度: %.0f)\n", i+1, article.Member, article.Score)
		}
	}
	
	fmt.Println("优势：动态热度排序，支持热度阈值过滤，实时排行更新")
}

// 3. 任务优先级队列场景
func priorityQueueExample(ctx context.Context) {
	taskQueueKey := "tasks:priority"
	
	fmt.Println("⚡ 任务优先级队列:")
	
	// 任务优先级数据（数字越大优先级越高）
	tasks := []redis.Z{
		{Score: 1, Member: "清理临时文件"},
		{Score: 5, Member: "处理用户反馈"},
		{Score: 9, Member: "修复安全漏洞"},
		{Score: 3, Member: "更新文档"},
		{Score: 7, Member: "数据库备份"},
		{Score: 2, Member: "发送邮件通知"},
		{Score: 8, Member: "系统性能监控"},
	}
	
	fmt.Println("📝 添加任务到队列:")
	for i, task := range tasks {
		err := redisClient.ZAdd(ctx, taskQueueKey, &task).Err()
		if err != nil {
			fmt.Printf("❌ 添加任务失败: %v\n", err)
			continue
		}
		fmt.Printf("  %d. %s (优先级: %.0f)\n", i+1, task.Member, task.Score)
	}
	
	// 紧急任务插入
	fmt.Println("\n🚨 紧急任务插入:")
	emergencyTask := &redis.Z{Score: 10, Member: "处理服务器宕机"}
	err := redisClient.ZAdd(ctx, taskQueueKey, emergencyTask).Err()
	if err != nil {
		fmt.Printf("❌ 添加紧急任务失败: %v\n", err)
	} else {
		fmt.Printf("✓ 紧急任务已插入: %s (优先级: %.0f)\n", 
			emergencyTask.Member, emergencyTask.Score)
	}
	
	// 按优先级处理任务（从高优先级开始）
	fmt.Println("\n⚙️ 按优先级处理任务:")
	for i := 1; i <= 4; i++ {
		// 获取最高优先级的任务
		highestPriorityTasks, err := redisClient.ZRevRangeWithScores(ctx, taskQueueKey, 0, 0).Result()
		if err != nil {
			fmt.Printf("❌ 获取最高优先级任务失败: %v\n", err)
			break
		}
		
		if len(highestPriorityTasks) == 0 {
			fmt.Println("  队列为空，没有任务需要处理")
			break
		}
		
		task := highestPriorityTasks[0]
		
		// 移除已处理的任务
		removed, err := redisClient.ZRem(ctx, taskQueueKey, task.Member).Result()
		if err != nil {
			fmt.Printf("❌ 移除任务失败: %v\n", err)
			continue
		}
		
		if removed > 0 {
			fmt.Printf("  处理任务 #%d: %s (优先级: %.0f) ✓\n", 
				i, task.Member, task.Score)
		}
		
		// 模拟任务处理时间
		time.Sleep(100 * time.Millisecond)
	}
	
	// 查看剩余任务
	fmt.Println("\n📋 剩余待处理任务:")
	remainingTasks, err := redisClient.ZRevRangeWithScores(ctx, taskQueueKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("❌ 获取剩余任务失败: %v\n", err)
	} else {
		if len(remainingTasks) == 0 {
			fmt.Println("  所有任务已处理完成")
		} else {
			for i, task := range remainingTasks {
				fmt.Printf("  %d. %s (优先级: %.0f)\n", i+1, task.Member, task.Score)
			}
		}
	}
	
	// 批量添加低优先级任务
	fmt.Println("\n📥 批量添加维护任务:")
	maintenanceTasks := []*redis.Z{
		{Score: 1, Member: "清理日志文件"},
		{Score: 1, Member: "优化数据库索引"},
		{Score: 2, Member: "更新系统补丁"},
	}
	
	err = redisClient.ZAdd(ctx, taskQueueKey, maintenanceTasks...).Err()
	if err != nil {
		fmt.Printf("❌ 批量添加任务失败: %v\n", err)
	} else {
		fmt.Printf("✓ 已添加 %d 个维护任务\n", len(maintenanceTasks))
	}
	
	// 获取特定优先级范围的任务
	fmt.Println("\n🔍 中等优先级任务 (优先级2-5):")
	mediumPriorityTasks, err := redisClient.ZRangeByScoreWithScores(ctx, taskQueueKey, &redis.ZRangeBy{
		Min: "2",
		Max: "5",
	}).Result()
	if err != nil {
		fmt.Printf("❌ 获取中等优先级任务失败: %v\n", err)
	} else {
		for i, task := range mediumPriorityTasks {
			fmt.Printf("  %d. %s (优先级: %.0f)\n", i+1, task.Member, task.Score)
		}
	}
	
	fmt.Println("优势：自动优先级排序，支持动态优先级调整，高效的任务调度")
}

// 4. 成绩管理场景
func scoreManagementExample(ctx context.Context) {
	scoresKey := "students:scores"
	
	fmt.Println("📚 学生成绩管理:")
	
	// 学生成绩数据
	students := []redis.Z{
		{Score: 85.5, Member: "张三"},
		{Score: 92.0, Member: "李四"},
		{Score: 78.5, Member: "王五"},
		{Score: 96.5, Member: "赵六"},
		{Score: 88.0, Member: "钱七"},
		{Score: 74.0, Member: "孙八"},
		{Score: 91.5, Member: "周九"},
		{Score: 82.5, Member: "吴十"},
	}
	
	fmt.Println("📝 录入学生成绩:")
	for i, student := range students {
		err := redisClient.ZAdd(ctx, scoresKey, &student).Err()
		if err != nil {
			fmt.Printf("❌ 录入成绩失败: %v\n", err)
			continue
		}
		fmt.Printf("  %d. %s: %.1f分\n", i+1, student.Member, student.Score)
	}
	
	// 成绩修正
	fmt.Println("\n✏️ 成绩修正:")
	
	// 张三重新考试，成绩提高
	newScore, err := redisClient.ZAdd(ctx, scoresKey, &redis.Z{Score: 89.0, Member: "张三"}).Result()
	if err != nil {
		fmt.Printf("❌ 修正张三成绩失败: %v\n", err)
	} else {
		if newScore == 0 {
			fmt.Println("✓ 张三成绩已修正为: 89.0分")
		}
	}
	
	// 获取成绩排名
	fmt.Println("\n🏆 成绩排名 (前5名):")
	topStudents, err := redisClient.ZRevRangeWithScores(ctx, scoresKey, 0, 4).Result()
	if err != nil {
		fmt.Printf("❌ 获取成绩排名失败: %v\n", err)
	} else {
		for i, student := range topStudents {
			fmt.Printf("  第%d名: %s - %.1f分\n", i+1, student.Member, student.Score)
		}
	}
	
	// 统计各分数段人数
	fmt.Println("\n📊 分数段统计:")
	
	// 优秀 (90分以上)
	excellentCount, err := redisClient.ZCount(ctx, scoresKey, "90", "+inf").Result()
	if err != nil {
		fmt.Printf("❌ 统计优秀人数失败: %v\n", err)
	} else {
		fmt.Printf("  优秀 (90分以上): %d人\n", excellentCount)
	}
	
	// 良好 (80-89分)
	goodCount, err := redisClient.ZCount(ctx, scoresKey, "80", "89.99").Result()
	if err != nil {
		fmt.Printf("❌ 统计良好人数失败: %v\n", err)
	} else {
		fmt.Printf("  良好 (80-89分): %d人\n", goodCount)
	}
	
	// 及格 (60-79分)
	passCount, err := redisClient.ZCount(ctx, scoresKey, "60", "79.99").Result()
	if err != nil {
		fmt.Printf("❌ 统计及格人数失败: %v\n", err)
	} else {
		fmt.Printf("  及格 (60-79分): %d人\n", passCount)
	}
	
	// 查询特定学生的成绩和排名
	fmt.Println("\n🔍 个人成绩查询:")
	queryStudents := []string{"张三", "李四", "孙八"}
	for _, studentName := range queryStudents {
		score, err := redisClient.ZScore(ctx, scoresKey, studentName).Result()
		if err != nil {
			fmt.Printf("❌ 查询%s成绩失败: %v\n", studentName, err)
			continue
		}
		
		rank, err := redisClient.ZRevRank(ctx, scoresKey, studentName).Result()
		if err != nil {
			fmt.Printf("❌ 查询%s排名失败: %v\n", studentName, err)
			continue
		}
		
		fmt.Printf("  %s: %.1f分, 排名第%d\n", studentName, score, rank+1)
	}
	
	// 获取平均分以上的学生
	fmt.Println("\n📈 平均分以上学生:")
	
	// 计算平均分（简单方法：获取所有成绩求平均）
	allScores, err := redisClient.ZRangeWithScores(ctx, scoresKey, 0, -1).Result()
	if err != nil {
		fmt.Printf("❌ 获取所有成绩失败: %v\n", err)
	} else {
		totalScore := 0.0
		for _, student := range allScores {
			totalScore += student.Score
		}
		avgScore := totalScore / float64(len(allScores))
		fmt.Printf("  班级平均分: %.1f\n", avgScore)
		
		// 获取平均分以上的学生
		aboveAvgStudents, err := redisClient.ZRangeByScoreWithScores(ctx, scoresKey, &redis.ZRangeBy{
			Min: fmt.Sprintf("%.1f", avgScore),
			Max: "+inf",
		}).Result()
		if err != nil {
			fmt.Printf("❌ 获取平均分以上学生失败: %v\n", err)
		} else {
			fmt.Printf("  平均分以上学生 (%d人):\n", len(aboveAvgStudents))
			for i, student := range aboveAvgStudents {
				fmt.Printf("    %d. %s: %.1f分\n", i+1, student.Member, student.Score)
			}
		}
	}
	
	fmt.Println("优势：自动成绩排序，支持分数段统计，高效的排名查询")
}

// 5. 时间序列数据场景
func timeSeriesExample(ctx context.Context) {
	timeSeriesKey := "events:timeline"
	
	fmt.Println("⏰ 时间序列事件管理:")
	
	// 使用时间戳作为分数的事件数据
	baseTime := time.Now().Add(-2 * time.Hour)
	events := []redis.Z{
		{Score: float64(baseTime.Unix()), Member: "系统启动"},
		{Score: float64(baseTime.Add(10 * time.Minute).Unix()), Member: "用户登录: user001"},
		{Score: float64(baseTime.Add(25 * time.Minute).Unix()), Member: "数据库连接建立"},
		{Score: float64(baseTime.Add(45 * time.Minute).Unix()), Member: "处理订单: order123"},
		{Score: float64(baseTime.Add(1 * time.Hour).Unix()), Member: "缓存清理"},
		{Score: float64(baseTime.Add(90 * time.Minute).Unix()), Member: "用户登出: user001"},
		{Score: float64(baseTime.Add(2 * time.Hour).Unix()), Member: "系统备份完成"},
	}
	
	fmt.Println("📝 记录时间序列事件:")
	for i, event := range events {
		err := redisClient.ZAdd(ctx, timeSeriesKey, &event).Err()
		if err != nil {
			fmt.Printf("❌ 记录事件失败: %v\n", err)
			continue
		}
		eventTime := time.Unix(int64(event.Score), 0)
		fmt.Printf("  %d. [%s] %s\n", i+1, eventTime.Format("15:04:05"), event.Member)
	}
	
	// 添加当前时间的新事件
	fmt.Println("\n📥 添加新事件:")
	newEvent := &redis.Z{
		Score:  float64(time.Now().Unix()),
		Member: "新用户注册: user002",
	}
	err := redisClient.ZAdd(ctx, timeSeriesKey, newEvent).Err()
	if err != nil {
		fmt.Printf("❌ 添加新事件失败: %v\n", err)
	} else {
		eventTime := time.Unix(int64(newEvent.Score), 0)
		fmt.Printf("✓ [%s] %s\n", eventTime.Format("15:04:05"), newEvent.Member)
	}
	
	// 获取最近的事件
	fmt.Println("\n🕐 最近5个事件:")
	recentEvents, err := redisClient.ZRevRangeWithScores(ctx, timeSeriesKey, 0, 4).Result()
	if err != nil {
		fmt.Printf("❌ 获取最近事件失败: %v\n", err)
	} else {
		for i, event := range recentEvents {
			eventTime := time.Unix(int64(event.Score), 0)
			fmt.Printf("  %d. [%s] %s\n", i+1, eventTime.Format("15:04:05"), event.Member)
		}
	}
	
	// 获取特定时间范围的事件
	fmt.Println("\n📅 过去1小时内的事件:")
	oneHourAgo := time.Now().Add(-1 * time.Hour)
	now := time.Now()
	
	rangeEvents, err := redisClient.ZRangeByScoreWithScores(ctx, timeSeriesKey, &redis.ZRangeBy{
		Min: fmt.Sprintf("%.0f", float64(oneHourAgo.Unix())),
		Max: fmt.Sprintf("%.0f", float64(now.Unix())),
	}).Result()
	if err != nil {
		fmt.Printf("❌ 获取时间范围事件失败: %v\n", err)
	} else {
		if len(rangeEvents) == 0 {
			fmt.Println("  过去1小时内没有事件")
		} else {
			for i, event := range rangeEvents {
				eventTime := time.Unix(int64(event.Score), 0)
				fmt.Printf("  %d. [%s] %s\n", i+1, eventTime.Format("15:04:05"), event.Member)
			}
		}
	}
	
	// 清理过期事件（保留最近2小时的事件）
	fmt.Println("\n🗑️ 清理过期事件:")
	twoHoursAgo := time.Now().Add(-2 * time.Hour)
	removedCount, err := redisClient.ZRemRangeByScore(ctx, timeSeriesKey, 
		"-inf", fmt.Sprintf("%.0f", float64(twoHoursAgo.Unix()))).Result()
	if err != nil {
		fmt.Printf("❌ 清理过期事件失败: %v\n", err)
	} else {
		fmt.Printf("✓ 已清理 %d 个过期事件\n", removedCount)
	}
	
	// 获取事件总数
	totalEvents, err := redisClient.ZCard(ctx, timeSeriesKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取事件总数失败: %v\n", err)
	} else {
		fmt.Printf("✓ 当前事件总数: %d\n", totalEvents)
	}
	
	fmt.Println("优势：基于时间戳自动排序，支持时间范围查询，高效的数据归档")
}

// 6. 基本操作演示
func basicZSetOperationsExample(ctx context.Context) {
	demoKey := "demo:zset"
	
	fmt.Println("🔧 ZSet基本操作演示:")
	
	// 清理
	redisClient.Del(ctx, demoKey)
	
	// ZADD - 添加元素
	redisClient.ZAdd(ctx, demoKey, 
		&redis.Z{Score: 1, Member: "one"},
		&redis.Z{Score: 2, Member: "two"},
		&redis.Z{Score: 3, Member: "three"},
	)
	fmt.Println("✓ ZADD: 添加元素")
	
	// ZRANGE - 按索引范围获取（升序）
	ascending, _ := redisClient.ZRangeWithScores(ctx, demoKey, 0, -1).Result()
	fmt.Printf("✓ ZRANGE (升序): %v\n", ascending)
	
	// ZREVRANGE - 按索引范围获取（降序）
	descending, _ := redisClient.ZRevRangeWithScores(ctx, demoKey, 0, -1).Result()
	fmt.Printf("✓ ZREVRANGE (降序): %v\n", descending)
	
	// ZCARD - 获取元素数量
	count, _ := redisClient.ZCard(ctx, demoKey).Result()
	fmt.Printf("✓ ZCARD: %d\n", count)
	
	// ZSCORE - 获取元素分数
	score, _ := redisClient.ZScore(ctx, demoKey, "two").Result()
	fmt.Printf("✓ ZSCORE 'two': %.0f\n", score)
	
	// ZRANK - 获取元素排名（升序）
	rank, _ := redisClient.ZRank(ctx, demoKey, "two").Result()
	fmt.Printf("✓ ZRANK 'two': %d\n", rank)
	
	// ZINCRBY - 增加元素分数
	newScore, _ := redisClient.ZIncrBy(ctx, demoKey, 1.5, "two").Result()
	fmt.Printf("✓ ZINCRBY 'two' +1.5: %.1f\n", newScore)
	
	// ZCOUNT - 统计分数范围内的元素数量
	rangeCount, _ := redisClient.ZCount(ctx, demoKey, "1", "3").Result()
	fmt.Printf("✓ ZCOUNT [1,3]: %d\n", rangeCount)
	
	// ZRANGEBYSCORE - 按分数范围获取
	byScore, _ := redisClient.ZRangeByScoreWithScores(ctx, demoKey, &redis.ZRangeBy{
		Min: "2",
		Max: "4",
	}).Result()
	fmt.Printf("✓ ZRANGEBYSCORE [2,4]: %v\n", byScore)
	
	// ZREM - 移除元素
	removed, _ := redisClient.ZRem(ctx, demoKey, "one").Result()
	fmt.Printf("✓ ZREM 'one': %d个元素被移除\n", removed)
	
	// 最终状态
	final, _ := redisClient.ZRangeWithScores(ctx, demoKey, 0, -1).Result()
	fmt.Printf("✓ 最终状态: %v\n", final)
	
	fmt.Println("✓ ZSet基本操作演示完成")
}

package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
)

// 全局 Redis 客户端变量
var redisClient *redis.Client

// 初始化 Redis 客户端连接
func init() {
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379", // Redis 服务器地址
		Password: "",               // Redis 密码（无密码则为空字符串）
		DB:       0,                // 使用的数据库编号，默认0
	})
}

func main() {
	ctx := context.Background()

	fmt.Println("=== Redis Hash 类型详解 ===\n")
	fmt.Println("Hash 是 Redis 中的字典结构，适合存储对象数据。")
	fmt.Println("每个 Hash 可以存储多个字段-值对，最多可存储 2^32-1 个字段。")
	fmt.Println("相比于用多个 String 键存储对象属性，Hash 更节省内存且操作更高效。")

	// 清理环境
	redisClient.Del(ctx, "user:1001", "product:10086", "cart:user123", "config:app", "article:comments:12345", "stats:website")

	// 1. 用户信息存储场景 - 最典型的Hash使用场景
	fmt.Println("\n1. 用户信息存储场景:")
	fmt.Println("应用场景：用户资料、商品信息、配置数据等结构化数据存储")
	userProfileExample(ctx)

	// 2. 购物车场景 - 动态字段管理
	fmt.Println("\n2. 购物车场景:")
	fmt.Println("应用场景：购物车商品管理、收藏夹、临时数据集合等")
	shoppingCartExample(ctx)

	// 3. 配置信息场景 - 应用配置管理
	fmt.Println("\n3. 配置信息场景:")
	fmt.Println("应用场景：应用配置、系统参数、功能开关等")
	configurationExample(ctx)

	// 4. 实时计数器场景 - 多维度统计
	fmt.Println("\n4. 实时计数器场景:")
	fmt.Println("应用场景：网站统计、用户行为分析、实时监控指标等")
	realTimeCounterExample(ctx)

	// 5. 评论系统场景 - 复杂数据结构
	fmt.Println("\n5. 评论系统场景:")
	fmt.Println("应用场景：评论管理、点赞系统、内容审核等")
	commentSystemExample(ctx)

	// 6. 基本操作演示
	fmt.Println("\n6. 基本操作演示:")
	basicHashOperationsExample(ctx)
}

// 1. 用户信息存储场景
func userProfileExample(ctx context.Context) {
	userKey := "user:1001"
	
	fmt.Println("👤 用户注册，创建用户资料:")
	
	// 创建用户资料（一次性设置多个字段）
	userProfile := map[string]interface{}{
		"id":          1001,
		"username":    "zhangsan",
		"email":       "<EMAIL>",
		"phone":       "13800138000",
		"age":         28,
		"city":        "北京",
		"gender":      "male",
		"created_at":  time.Now().Format("2006-01-02 15:04:05"),
		"last_login":  "",
		"status":      "active",
		"vip_level":   1,
	}
	
	err := redisClient.HMSet(ctx, userKey, userProfile).Err()
	if err != nil {
		fmt.Printf("❌ 创建用户资料失败: %v\n", err)
		return
	}
	fmt.Println("✓ 用户资料创建成功")
	
	// 获取用户基本信息
	basicInfo, err := redisClient.HMGet(ctx, userKey, "username", "email", "city").Result()
	if err != nil {
		fmt.Printf("❌ 获取用户基本信息失败: %v\n", err)
	} else {
		fmt.Printf("✓ 用户基本信息 - 用户名: %v, 邮箱: %v, 城市: %v\n", 
			basicInfo[0], basicInfo[1], basicInfo[2])
	}
	
	// 更新用户登录时间
	err = redisClient.HSet(ctx, userKey, "last_login", time.Now().Format("2006-01-02 15:04:05")).Err()
	if err != nil {
		fmt.Printf("❌ 更新登录时间失败: %v\n", err)
	} else {
		fmt.Println("✓ 用户登录时间已更新")
	}
	
	// 用户升级VIP
	newVipLevel, err := redisClient.HIncrBy(ctx, userKey, "vip_level", 1).Result()
	if err != nil {
		fmt.Printf("❌ 升级VIP失败: %v\n", err)
	} else {
		fmt.Printf("✓ 用户VIP等级升级到: %d\n", newVipLevel)
	}
	
	// 获取完整用户资料
	fmt.Println("\n📋 完整用户资料:")
	allFields, err := redisClient.HGetAll(ctx, userKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取完整资料失败: %v\n", err)
	} else {
		for field, value := range allFields {
			fmt.Printf("  %s: %s\n", field, value)
		}
	}
	
	fmt.Println("优势：结构化存储，字段独立更新，内存效率高")
}

// 2. 购物车场景
func shoppingCartExample(ctx context.Context) {
	cartKey := "cart:user123"
	
	fmt.Println("🛒 购物车管理:")
	
	// 添加商品到购物车（商品ID作为字段，数量作为值）
	products := map[string]interface{}{
		"product:1001": 2,  // iPhone 14, 数量2
		"product:1002": 1,  // MacBook Pro, 数量1
		"product:1003": 3,  // AirPods, 数量3
	}
	
	err := redisClient.HMSet(ctx, cartKey, products).Err()
	if err != nil {
		fmt.Printf("❌ 添加商品失败: %v\n", err)
		return
	}
	fmt.Println("✓ 商品已添加到购物车")
	
	// 增加某个商品的数量
	newQuantity, err := redisClient.HIncrBy(ctx, cartKey, "product:1001", 1).Result()
	if err != nil {
		fmt.Printf("❌ 增加商品数量失败: %v\n", err)
	} else {
		fmt.Printf("✓ iPhone 14 数量增加到: %d\n", newQuantity)
	}
	
	// 检查购物车中是否有某个商品
	exists, err := redisClient.HExists(ctx, cartKey, "product:1004").Result()
	if err != nil {
		fmt.Printf("❌ 检查商品失败: %v\n", err)
	} else {
		fmt.Printf("✓ 购物车中是否有 product:1004: %t\n", exists)
	}
	
	// 获取购物车商品总数
	totalItems, err := redisClient.HLen(ctx, cartKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取商品总数失败: %v\n", err)
	} else {
		fmt.Printf("✓ 购物车中商品种类数: %d\n", totalItems)
	}
	
	// 计算购物车商品总数量
	fmt.Println("\n📦 购物车详情:")
	cartItems, err := redisClient.HGetAll(ctx, cartKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取购物车详情失败: %v\n", err)
	} else {
		totalQuantity := 0
		for productID, quantityStr := range cartItems {
			quantity, _ := strconv.Atoi(quantityStr)
			totalQuantity += quantity
			fmt.Printf("  %s: %d 件\n", productID, quantity)
		}
		fmt.Printf("✓ 购物车总数量: %d 件\n", totalQuantity)
	}
	
	// 移除某个商品
	removed, err := redisClient.HDel(ctx, cartKey, "product:1003").Result()
	if err != nil {
		fmt.Printf("❌ 移除商品失败: %v\n", err)
	} else {
		fmt.Printf("✓ 已移除 %d 个商品\n", removed)
	}
	
	fmt.Println("优势：动态字段管理，高效的商品数量操作，支持原子性更新")
}

// 3. 配置信息场景
func configurationExample(ctx context.Context) {
	configKey := "config:app"
	
	fmt.Println("⚙️ 应用配置管理:")
	
	// 设置应用配置
	appConfig := map[string]interface{}{
		"app_name":           "MyApp",
		"version":            "1.2.3",
		"debug_mode":         "false",
		"max_connections":    100,
		"timeout_seconds":    30,
		"enable_cache":       "true",
		"cache_ttl":          3600,
		"log_level":          "info",
		"maintenance_mode":   "false",
		"feature_flag_new_ui": "true",
	}
	
	err := redisClient.HMSet(ctx, configKey, appConfig).Err()
	if err != nil {
		fmt.Printf("❌ 设置配置失败: %v\n", err)
		return
	}
	fmt.Println("✓ 应用配置已设置")
	
	// 获取关键配置
	keyConfigs, err := redisClient.HMGet(ctx, configKey, "debug_mode", "max_connections", "timeout_seconds").Result()
	if err != nil {
		fmt.Printf("❌ 获取关键配置失败: %v\n", err)
	} else {
		fmt.Printf("✓ 关键配置 - 调试模式: %v, 最大连接数: %v, 超时时间: %v秒\n", 
			keyConfigs[0], keyConfigs[1], keyConfigs[2])
	}
	
	// 动态调整配置
	fmt.Println("\n🔧 动态配置调整:")
	
	// 开启维护模式
	err = redisClient.HSet(ctx, configKey, "maintenance_mode", "true").Err()
	if err != nil {
		fmt.Printf("❌ 开启维护模式失败: %v\n", err)
	} else {
		fmt.Println("✓ 维护模式已开启")
	}
	
	// 增加最大连接数
	newMaxConn, err := redisClient.HIncrBy(ctx, configKey, "max_connections", 50).Result()
	if err != nil {
		fmt.Printf("❌ 调整最大连接数失败: %v\n", err)
	} else {
		fmt.Printf("✓ 最大连接数调整为: %d\n", newMaxConn)
	}
	
	// 获取所有配置字段名
	configFields, err := redisClient.HKeys(ctx, configKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取配置字段失败: %v\n", err)
	} else {
		fmt.Printf("✓ 配置字段列表: %v\n", configFields)
	}
	
	// 检查特定功能是否启用
	featureEnabled, err := redisClient.HGet(ctx, configKey, "feature_flag_new_ui").Result()
	if err != nil {
		fmt.Printf("❌ 检查功能开关失败: %v\n", err)
	} else {
		fmt.Printf("✓ 新UI功能开关: %s\n", featureEnabled)
	}
	
	fmt.Println("优势：配置集中管理，支持热更新，字段级别的精确控制")
}

// 4. 实时计数器场景
func realTimeCounterExample(ctx context.Context) {
	statsKey := "stats:website"
	
	fmt.Println("📊 网站实时统计:")
	
	// 初始化统计数据
	initialStats := map[string]interface{}{
		"page_views":     0,
		"unique_visitors": 0,
		"user_signups":   0,
		"orders":         0,
		"revenue":        0,
	}
	
	err := redisClient.HMSet(ctx, statsKey, initialStats).Err()
	if err != nil {
		fmt.Printf("❌ 初始化统计失败: %v\n", err)
		return
	}
	fmt.Println("✓ 统计数据已初始化")
	
	// 模拟网站活动
	fmt.Println("\n📈 模拟网站活动:")
	
	// 页面访问
	for i := 1; i <= 5; i++ {
		newViews, err := redisClient.HIncrBy(ctx, statsKey, "page_views", 1).Result()
		if err != nil {
			fmt.Printf("❌ 更新页面访问失败: %v\n", err)
		} else {
			fmt.Printf("  页面访问 #%d，总访问量: %d\n", i, newViews)
		}
	}
	
	// 用户注册
	newSignups, err := redisClient.HIncrBy(ctx, statsKey, "user_signups", 3).Result()
	if err != nil {
		fmt.Printf("❌ 更新注册数失败: %v\n", err)
	} else {
		fmt.Printf("✓ 新增3个用户注册，总注册数: %d\n", newSignups)
	}
	
	// 订单和收入
	newOrders, err := redisClient.HIncrBy(ctx, statsKey, "orders", 2).Result()
	if err != nil {
		fmt.Printf("❌ 更新订单数失败: %v\n", err)
	} else {
		fmt.Printf("✓ 新增2个订单，总订单数: %d\n", newOrders)
	}
	
	// 增加收入（以分为单位）
	newRevenue, err := redisClient.HIncrBy(ctx, statsKey, "revenue", 29900).Result() // 299.00元
	if err != nil {
		fmt.Printf("❌ 更新收入失败: %v\n", err)
	} else {
		fmt.Printf("✓ 新增收入，总收入: %.2f元\n", float64(newRevenue)/100)
	}
	
	// 获取实时统计报告
	fmt.Println("\n📋 实时统计报告:")
	allStats, err := redisClient.HGetAll(ctx, statsKey).Result()
	if err != nil {
		fmt.Printf("❌ 获取统计报告失败: %v\n", err)
	} else {
		for metric, value := range allStats {
			if metric == "revenue" {
				revenue, _ := strconv.ParseInt(value, 10, 64)
				fmt.Printf("  %s: %.2f元\n", metric, float64(revenue)/100)
			} else {
				fmt.Printf("  %s: %s\n", metric, value)
			}
		}
	}
	
	fmt.Println("优势：多维度实时统计，原子性计数操作，高性能数据聚合")
}

// 5. 评论系统场景
func commentSystemExample(ctx context.Context) {
	commentKey := "article:comments:12345"
	
	fmt.Println("💬 文章评论系统:")
	
	// 文章评论统计信息
	commentStats := map[string]interface{}{
		"total_comments":  0,
		"approved_comments": 0,
		"pending_comments":  0,
		"spam_comments":     0,
		"likes":            0,
		"dislikes":         0,
		"last_comment_time": "",
		"top_commenter":    "",
	}
	
	err := redisClient.HMSet(ctx, commentKey, commentStats).Err()
	if err != nil {
		fmt.Printf("❌ 初始化评论统计失败: %v\n", err)
		return
	}
	fmt.Println("✓ 评论系统已初始化")
	
	// 新增评论
	fmt.Println("\n📝 处理新评论:")
	
	// 总评论数增加
	totalComments, err := redisClient.HIncrBy(ctx, commentKey, "total_comments", 1).Result()
	if err != nil {
		fmt.Printf("❌ 更新总评论数失败: %v\n", err)
	} else {
		fmt.Printf("✓ 新增评论，总评论数: %d\n", totalComments)
	}
	
	// 待审核评论增加
	pendingComments, err := redisClient.HIncrBy(ctx, commentKey, "pending_comments", 1).Result()
	if err != nil {
		fmt.Printf("❌ 更新待审核评论失败: %v\n", err)
	} else {
		fmt.Printf("✓ 待审核评论数: %d\n", pendingComments)
	}
	
	// 更新最后评论时间
	err = redisClient.HSet(ctx, commentKey, "last_comment_time", time.Now().Format("2006-01-02 15:04:05")).Err()
	if err != nil {
		fmt.Printf("❌ 更新评论时间失败: %v\n", err)
	} else {
		fmt.Println("✓ 最后评论时间已更新")
	}
	
	// 评论审核通过
	fmt.Println("\n✅ 评论审核:")
	
	// 待审核减1，已通过加1
	redisClient.HIncrBy(ctx, commentKey, "pending_comments", -1)
	approvedComments, err := redisClient.HIncrBy(ctx, commentKey, "approved_comments", 1).Result()
	if err != nil {
		fmt.Printf("❌ 审核评论失败: %v\n", err)
	} else {
		fmt.Printf("✓ 评论审核通过，已通过评论数: %d\n", approvedComments)
	}
	
	// 评论获得点赞
	likes, err := redisClient.HIncrBy(ctx, commentKey, "likes", 5).Result()
	if err != nil {
		fmt.Printf("❌ 更新点赞数失败: %v\n", err)
	} else {
		fmt.Printf("✓ 评论获得5个点赞，总点赞数: %d\n", likes)
	}
	
	// 获取评论统计摘要
	fmt.Println("\n📊 评论统计摘要:")
	summary, err := redisClient.HMGet(ctx, commentKey, 
		"total_comments", "approved_comments", "pending_comments", "likes").Result()
	if err != nil {
		fmt.Printf("❌ 获取统计摘要失败: %v\n", err)
	} else {
		fmt.Printf("✓ 总评论: %v, 已通过: %v, 待审核: %v, 点赞数: %v\n", 
			summary[0], summary[1], summary[2], summary[3])
	}
	
	fmt.Println("优势：复杂数据结构管理，多字段原子性操作，实时统计更新")
}

// 6. 基本操作演示
func basicHashOperationsExample(ctx context.Context) {
	demoKey := "demo:hash"
	
	fmt.Println("🔧 Hash基本操作演示:")
	
	// 清理
	redisClient.Del(ctx, demoKey)
	
	// HSET - 设置单个字段
	redisClient.HSet(ctx, demoKey, "field1", "value1")
	fmt.Println("✓ HSET: 设置单个字段")
	
	// HMSET - 设置多个字段
	redisClient.HMSet(ctx, demoKey, map[string]interface{}{
		"field2": "value2",
		"field3": 100,
		"field4": "value4",
	})
	fmt.Println("✓ HMSET: 设置多个字段")
	
	// HGET - 获取单个字段
	value, _ := redisClient.HGet(ctx, demoKey, "field1").Result()
	fmt.Printf("✓ HGET field1: %s\n", value)
	
	// HMGET - 获取多个字段
	values, _ := redisClient.HMGet(ctx, demoKey, "field1", "field2", "field3").Result()
	fmt.Printf("✓ HMGET: %v\n", values)
	
	// HGETALL - 获取所有字段
	allFields, _ := redisClient.HGetAll(ctx, demoKey).Result()
	fmt.Printf("✓ HGETALL: %v\n", allFields)
	
	// HKEYS - 获取所有字段名
	keys, _ := redisClient.HKeys(ctx, demoKey).Result()
	fmt.Printf("✓ HKEYS: %v\n", keys)
	
	// HVALS - 获取所有值
	vals, _ := redisClient.HVals(ctx, demoKey).Result()
	fmt.Printf("✓ HVALS: %v\n", vals)
	
	// HLEN - 获取字段数量
	length, _ := redisClient.HLen(ctx, demoKey).Result()
	fmt.Printf("✓ HLEN: %d\n", length)
	
	// HEXISTS - 检查字段是否存在
	exists, _ := redisClient.HExists(ctx, demoKey, "field1").Result()
	fmt.Printf("✓ HEXISTS field1: %t\n", exists)
	
	// HINCRBY - 字段值增加
	newValue, _ := redisClient.HIncrBy(ctx, demoKey, "field3", 50).Result()
	fmt.Printf("✓ HINCRBY field3 +50: %d\n", newValue)
	
	// HDEL - 删除字段
	deleted, _ := redisClient.HDel(ctx, demoKey, "field4").Result()
	fmt.Printf("✓ HDEL field4: %d个字段被删除\n", deleted)
	
	fmt.Println("✓ Hash基本操作演示完成")
}

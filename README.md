# Redis 学习指南

这个项目包含了 Redis 五种主要数据类型的示例代码和应用场景，以及一些高级特性的演示。

## 目录结构

- `examples/string` - 字符串类型示例
- `examples/hash` - 哈希类型示例
- `examples/list` - 列表类型示例
- `examples/set` - 集合类型示例
- `examples/zset` - 有序集合类型示例
- `examples/advanced` - 高级特性（事务、发布订阅等）

## 快速入门

运行示例代码：

```bash
# 字符串类型示例
go run examples/string/string_demo.go

# 哈希类型示例
go run examples/hash/hash_demo.go

# 列表类型示例
go run examples/list/list_demo.go

# 集合类型示例
go run examples/set/set_demo.go

# 有序集合类型示例
go run examples/zset/zset_demo.go

# 事务示例
go run examples/advanced/transaction_demo.go

# 发布订阅示例
go run examples/advanced/pubsub_demo.go
```

## Redis 数据类型总览

### 1. String (字符串)

最基本的数据类型，可以存储文本、数字、二进制数据等。

**应用场景**：
- 缓存数据（如 HTML 页面）
- 计数器（如访问次数）
- 分布式锁（使用 SETNX）
- 会话存储（如用户登录信息）

### 2. Hash (哈希)

存储对象数据，每个 Hash 可以存储多个字段-值对。

**应用场景**：
- 用户信息存储
- 购物车
- 对象缓存
- 配置信息存储

### 3. List (列表)

有序的字符串列表，支持双向操作。

**应用场景**：
- 消息队列
- 最新动态列表
- 任务队列
- 社交网络的时间线

### 4. Set (集合)

无序、唯一的字符串集合。

**应用场景**：
- 标签系统
- 唯一访问者统计
- 黑名单/白名单
- 共同好友查找

### 5. Sorted Set (有序集合)

有序、唯一的字符串集合，每个元素关联一个分数。

**应用场景**：
- 排行榜
- 优先级队列
- 范围查询
- 带权重的任务调度

## 高级特性

### 1. 事务

将多个命令打包成一个原子操作。

**应用场景**：
- 转账操作
- 库存管理
- 复合计数器更新

### 2. 发布订阅

实现消息的发布和订阅功能。

**应用场景**：
- 实时通知系统
- 聊天应用
- 系统监控
- 任务分发
